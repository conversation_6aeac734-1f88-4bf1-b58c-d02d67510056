'use client'
import React from 'react'
import <PERSON> from 'next/link'
import { Brain, Mail, Phone, MapPin, Twitter, Linkedin, Github } from 'lucide-react'
import { cn } from '@/lib/utils'

const footerLinks = {
  product: [
    { name: 'Features', href: '#features' },
    { name: 'AI Models', href: '#models' },
    { name: 'Integrations', href: '#integrations' },
    { name: 'API Documentation', href: '#api' },
  ],
  company: [
    { name: 'About Us', href: '#about' },
    { name: 'Careers', href: '#careers' },
    { name: 'Blog', href: '#blog' },
    { name: 'Press', href: '#press' },
  ],
  resources: [
    { name: 'Documentation', href: '#docs' },
    { name: 'Tutoria<PERSON>', href: '#tutorials' },
    { name: 'Community', href: '#community' },
    { name: 'Support', href: '#support' },
  ],
  legal: [
    { name: 'Privacy Policy', href: '#privacy' },
    { name: 'Terms of Service', href: '#terms' },
    { name: 'Cookie Policy', href: '#cookies' },
    { name: 'GDPR', href: '#gdpr' },
  ],
}

const socialLinks = [
  { name: 'Twitter', href: 'https://twitter.com/thesemantics', icon: Twitter },
  { name: 'LinkedIn', href: 'https://www.linkedin.com/company/thesemanticsco/', icon: Linkedin },
  { name: 'GitHub - Umar', href: 'https://github.com/Tooti12/', icon: Github },
  { name: 'GitHub - Tahir', href: 'https://github.com/Tahir-Siddique/', icon: Github },
]

export function Footer() {
  return (
    <footer className="bg-background border-t relative overflow-hidden">
      {/* DNA Footer Background */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-cyan-500/20"></div>
        <div className="absolute top-4 left-10 w-8 h-8 border-2 border-blue-400 rounded-full animate-pulse"></div>
        <div className="absolute top-8 right-20 w-6 h-6 border-2 border-purple-400 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute bottom-4 left-1/3 w-10 h-10 border-2 border-cyan-400 rounded-full animate-pulse delay-2000"></div>
        <div className="absolute bottom-8 right-1/4 w-7 h-7 border-2 border-indigo-400 rounded-full animate-pulse delay-3000"></div>
      </div>
      <div className="mx-auto max-w-7xl px-6 lg:px-12 relative z-10">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <Brain className="h-8 w-8 text-blue-600" />
                <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  The Semantics
                </span>
              </div>
              <p className="text-muted-foreground mb-6 max-w-sm">
                Building the DNA of artificial intelligence.
                Unlock the genetic code of semantic understanding and intelligent automation.
              </p>
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Mail className="h-4 w-4" />
                  <a href="mailto:<EMAIL>" className="hover:text-foreground transition-colors"><EMAIL></a>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Phone className="h-4 w-4" />
                  <span>+****************</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <MapPin className="h-4 w-4" />
                  <span>San Francisco, CA</span>
                </div>
              </div>
            </div>

            {/* Product Links */}
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2">
                {footerLinks.product.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Company Links */}
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2">
                {footerLinks.company.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Resources Links */}
            <div>
              <h3 className="font-semibold mb-4">Resources</h3>
              <ul className="space-y-2">
                {footerLinks.resources.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Legal Links */}
            <div>
              <h3 className="font-semibold mb-4">Legal</h3>
              <ul className="space-y-2">
                {footerLinks.legal.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-muted-foreground">
              © 2024 The Semantics. All rights reserved.
            </div>
            <div className="flex items-center space-x-4">
              {socialLinks.map((social) => (
                <Link
                  key={social.name}
                  href={social.href}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                  aria-label={social.name}
                >
                  <social.icon className="h-5 w-5" />
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
