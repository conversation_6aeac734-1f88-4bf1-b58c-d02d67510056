import Link from 'next/link';

export function Navigation() {
  const links = [
    { href: 'https://fromsemantics.thesemantics.co/', label: 'Desktop Development' },
    { href: 'https://getthesemantics.thesemantics.co/', label: 'Mobile Apps' },
    { href: 'https://heyfromsemantics.thesemantics.co/#careers', label: 'AI Platform' },
    { href: 'https://semanticsconnect.thesemantics.co/', label: 'Web Scraping' },
    { href: 'https://thesemantics.com', label: 'Main Site' }
  ];

  return (
    <nav aria-label="Main navigation" className="hidden lg:flex space-x-6">
      {links.map((link, index) => (
        <Link 
          key={index}
          href={link.href}
          className="text-white/70 hover:text-white transition-colors"
          rel="noopener"
        >
          {link.label}
        </Link>
      ))}
    </nav>
  );
}