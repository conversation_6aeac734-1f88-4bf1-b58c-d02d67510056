module.exports = {

"[project]/src/components/ui/hero-section.tsx [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_three_build_three_core_a75ea919.js",
  "server/chunks/ssr/node_modules_three_build_three_module_bc28cc46.js",
  "server/chunks/ssr/node_modules_three_build_three_module_c9a5c6ae.js",
  "server/chunks/ssr/node_modules_react-reconciler_4b3d0196._.js",
  "server/chunks/ssr/node_modules_@react-three_fiber_dist_abeaa19d._.js",
  "server/chunks/ssr/node_modules_5731c1f7._.js",
  "server/chunks/ssr/src_components_ui_hero-section_tsx_a7ee641a._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/ui/hero-section.tsx [app-ssr] (ecmascript)");
    });
});
}),

};