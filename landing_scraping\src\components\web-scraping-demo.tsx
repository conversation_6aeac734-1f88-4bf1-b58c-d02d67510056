"use client";

import {
  Code,
  Database,
  Globe,
  Shield,
  Zap,
  Search,
  Filter,
  Download,
  Settings,
  BarChart3,
  Users,
  Award,
  Clock,
  Target,
  Github,
  Linkedin,
  Twitter
} from "lucide-react";
import RadialOrbitalTimeline from "@/components/ui/radial-orbital-timeline";
import ClientOnly from "@/components/client-only";
import { SpiralAnimation } from "@/components/ui/spiral-animation";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

const webScrapingTimelineData = [
  {
    id: 1,
    title: "Target Analysis",
    date: "Phase 1",
    content: "Analyze target websites, identify data structures, and understand anti-bot measures. Study robots.txt, rate limits, and content patterns.",
    category: "Analysis",
    icon: Search,
    relatedIds: [2, 3],
    status: "completed" as const,
    energy: 95,
  },
  {
    id: 2,
    title: "BeautifulSoup Setup",
    date: "Phase 2",
    content: "Configure BeautifulSoup for HTML parsing with lxml parser. Set up CSS selectors and XPath expressions for precise data extraction.",
    category: "Parser",
    icon: Code,
    relatedIds: [1, 3, 4],
    status: "completed" as const,
    energy: 90,
  },
  {
    id: 3,
    title: "Scrapy Framework",
    date: "Phase 3",
    content: "Implement Scrapy spiders for large-scale crawling. Configure pipelines, middlewares, and custom settings for optimal performance.",
    category: "Framework",
    icon: Globe,
    relatedIds: [1, 2, 5],
    status: "completed" as const,
    energy: 85,
  },
  {
    id: 4,
    title: "Data Processing",
    date: "Phase 4",
    content: "Use Pandas for data cleaning, transformation, and validation. Implement NumPy for numerical operations and statistical analysis.",
    category: "Processing",
    icon: Database,
    relatedIds: [2, 5, 6],
    status: "in-progress" as const,
    energy: 75,
  },
  {
    id: 5,
    title: "Anti-Detection",
    date: "Phase 5",
    content: "Implement rotating proxies, user agents, and request delays. Use Selenium for JavaScript-heavy sites and CAPTCHA handling.",
    category: "Security",
    icon: Shield,
    relatedIds: [3, 4, 7],
    status: "in-progress" as const,
    energy: 70,
  },
  {
    id: 6,
    title: "Data Storage",
    date: "Phase 6",
    content: "Configure MongoDB with PyMongo for flexible document storage. Implement data indexing and efficient query patterns.",
    category: "Storage",
    icon: Download,
    relatedIds: [4, 8],
    status: "in-progress" as const,
    energy: 60,
  },
  {
    id: 7,
    title: "Performance Tuning",
    date: "Phase 7",
    content: "Optimize with asyncio for concurrent requests. Implement connection pooling and memory management for high-throughput scraping.",
    category: "Optimization",
    icon: Zap,
    relatedIds: [5, 8, 9],
    status: "pending" as const,
    energy: 45,
  },
  {
    id: 8,
    title: "Quality Control",
    date: "Phase 8",
    content: "Implement data validation pipelines, duplicate detection, and error handling. Monitor scraping success rates and data quality metrics.",
    category: "Quality",
    icon: Filter,
    relatedIds: [6, 7, 9],
    status: "pending" as const,
    energy: 35,
  },
  {
    id: 9,
    title: "Monitoring & Analytics",
    date: "Phase 9",
    content: "Set up real-time monitoring dashboards, performance metrics, and automated alerting systems for production scraping operations.",
    category: "Analytics",
    icon: BarChart3,
    relatedIds: [7, 8, 10],
    status: "pending" as const,
    energy: 25,
  },
  {
    id: 10,
    title: "Deployment",
    date: "Phase 10",
    content: "Deploy scalable scraping infrastructure with Docker containers, load balancing, and automated scaling based on demand.",
    category: "Deployment",
    icon: Settings,
    relatedIds: [9],
    status: "pending" as const,
    energy: 15,
  },
];

// About Section Component
const AboutSection = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const stats = [
    { icon: Users, number: "200+", label: "Scraping Projects" },
    { icon: Award, number: "4.9/5", label: "Client Rating" },
    { icon: Clock, number: "5+", label: "Years Experience" },
    { icon: Target, number: "99%", label: "Success Rate" },
  ];

  const team = [
    {
      name: "Umar Javaid",
      role: "Founder & CEO",
      image: "/umar-javaid.jpg",
      bio: "Expert in modern web technologies, AI integration, and scalable application architecture. Specializes in React, Node.js, and machine learning implementations.",
      social: { github: "#", linkedin: "#", twitter: "#" }
    },
    {
      name: "Tahir Siddique",
      role: "Co-Founder & Full Stack Developer",
      image: "/tahir-siddique.jpg",
      bio: "Full stack developer specializing in Python, web scraping, and automation solutions. Expert in creating scalable web applications with a 4.9/5 rating on Fiverr.",
      social: { github: "#", linkedin: "#", twitter: "#" }
    },
    {
      name: "James Mitchell",
      role: "UI/UX Designer & Frontend Specialist",
      image: "https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=300&h=300&fit=crop&crop=face",
      bio: "Creative designer with a passion for crafting intuitive user experiences. Combines aesthetic design with functional frontend development.",
      social: { github: "#", linkedin: "#", twitter: "#" }
    },
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-gray-900 to-black">
      <div className="container mx-auto px-4">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent">
              About Us
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              We're a passionate team of web scraping experts, data engineers, and automation specialists
              dedicated to creating ethical and efficient data extraction solutions.
            </p>
          </motion.div>

          {/* Stats */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="text-center group"
              >
                <div className="inline-flex p-4 rounded-xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/10 mb-4 group-hover:scale-110 transition-transform duration-300">
                  <stat.icon className="w-8 h-8 text-blue-400" />
                </div>
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-400 font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Company Story */}
          <motion.div
            variants={itemVariants}
            className="grid md:grid-cols-2 gap-12 items-center mb-20"
          >
            <div>
              <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Our Story
              </h3>
              <div className="space-y-4 text-gray-300 leading-relaxed">
                <p>
                  Founded in 2019, we started as a small team of passionate developers
                  who believed that ethical web scraping could transform how businesses
                  access and utilize web data.
                </p>
                <p>
                  Today, we've grown into a specialized web scraping agency,
                  helping startups and enterprises extract valuable data from the web
                  while maintaining the highest ethical and legal standards.
                </p>
                <p>
                  Our commitment to quality, compliance, and client success has made us
                  a trusted partner for companies looking to harness the power of web data.
                </p>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-square rounded-2xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/10 p-8 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-6xl mb-4">🕷️</div>
                  <div className="text-2xl font-bold text-white mb-2">Ethical First</div>
                  <div className="text-gray-400">Responsible web scraping practices</div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Team */}
          <motion.div variants={containerVariants}>
            <motion.div variants={itemVariants} className="text-center mb-12">
              <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Meet Our Team
              </h3>
              <p className="text-gray-300 max-w-2xl mx-auto">
                The talented individuals behind our success, each bringing unique expertise
                and passion to every project.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-3 gap-8">
              {team.map((member, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="group text-center"
                >
                  <div className="relative mb-6">
                    <div className="w-32 h-32 mx-auto rounded-full overflow-hidden border-4 border-white/10 group-hover:border-blue-400/50 transition-colors duration-300">
                      <img
                        src={member.image}
                        alt={member.name}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                    </div>
                  </div>
                  <h4 className="text-xl font-bold text-white mb-2">{member.name}</h4>
                  <p className="text-blue-400 font-medium mb-3">{member.role}</p>
                  <p className="text-gray-400 text-sm mb-4 leading-relaxed">{member.bio}</p>
                  <div className="flex justify-center space-x-3">
                    <a href={member.social.github} className="text-gray-400 hover:text-white transition-colors">
                      <Github className="w-5 h-5" />
                    </a>
                    <a href={member.social.linkedin} className="text-gray-400 hover:text-white transition-colors">
                      <Linkedin className="w-5 h-5" />
                    </a>
                    <a href={member.social.twitter} className="text-gray-400 hover:text-white transition-colors">
                      <Twitter className="w-5 h-5" />
                    </a>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export function WebScrapingDemo() {
  return (
    <div className="w-full bg-black">
      {/* Hero Section */}
      <section className="w-full h-screen bg-black relative" aria-label="Web scraping services demonstration">
      {/* Company Title Overlay */}
      <header className="absolute top-8 left-1/2 transform -translate-x-1/2 z-50 text-center">
        <h1 className="text-4xl md:text-6xl font-light tracking-widest text-white mb-2">
          Professional Web Scraping Services
        </h1>
        <h2 className="text-lg md:text-xl text-white/70 font-mono tracking-wide">
          Python • BeautifulSoup • Scrapy • Data Extraction
        </h2>
        <p className="text-sm md:text-base text-white/60 font-mono mt-2">
          BeautifulSoup • Scrapy • Data Extraction • API Development
        </p>
      </header>

      {/* Instructions Overlay */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-50 text-center">
        <p className="text-sm text-white/60 font-mono">
          Click on orbital nodes to explore web scraping phases • Connected nodes pulse when active
        </p>
      </div>

      {/* Spiral Animation Background */}
      <ClientOnly
        fallback={null}
      >
        <SpiralAnimation />
      </ClientOnly>

      {/* Orbital Timeline */}
      <main className="w-full h-full relative z-10" aria-label="Web scraping process demonstration">
        <ClientOnly
          fallback={
            <div className="w-full h-screen flex flex-col items-center justify-center bg-transparent overflow-hidden cursor-scraping relative z-10">
              <div className="relative w-full max-w-4xl h-full flex items-center justify-center">
                <div className="absolute w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 via-blue-500 to-teal-500 animate-pulse flex items-center justify-center z-10">
                  <div className="w-8 h-8 rounded-full bg-white/80 backdrop-blur-md"></div>
                </div>
                <div className="absolute w-96 h-96 rounded-full border border-white/10"></div>
              </div>
            </div>
          }
        >
          <RadialOrbitalTimeline timelineData={webScrapingTimelineData} />
        </ClientOnly>
      </main>
      </section>

      {/* About Section */}
      <AboutSection />

      {/* Reviews Section */}
      <section className="w-full py-20 bg-gradient-to-b from-gray-900 to-black" aria-label="Client reviews">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              What Our <span className="text-green-400">Clients Say</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Trusted by businesses worldwide for reliable web scraping solutions
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[
              {
                name: "DataTech Solutions",
                role: "CTO",
                company: "United States",
                rating: 5,
                text: "Outstanding web scraping service! They delivered exactly what we needed for our market research project. Professional, reliable, and efficient.",
                project: "E-commerce Data Extraction"
              },
              {
                name: "Global Analytics",
                role: "Data Manager",
                company: "United Kingdom",
                rating: 5,
                text: "Exceptional quality and attention to detail. The team understood our complex requirements and delivered clean, structured data on time.",
                project: "Real Estate Data Mining"
              },
              {
                name: "Market Insights Co",
                role: "Research Director",
                company: "Canada",
                rating: 5,
                text: "Highly recommend their web scraping services. They handled large-scale data extraction with precision and provided excellent documentation.",
                project: "Social Media Analytics"
              }
            ].map((review, index) => {
              // Generate unique scraping-themed placeholder
              const getScrapingPlaceholder = (name: string, index: number) => {
                const colors = ['#10B981', '#06B6D4', '#8B5CF6', '#F59E0B'];
                const color = colors[index % colors.length];
                const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
                return `data:image/svg+xml;base64,${btoa(`
                  <svg width="150" height="150" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                      <linearGradient id="scrapingGrad${index}" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
                        <stop offset="100%" style="stop-color:${color}60;stop-opacity:1" />
                      </linearGradient>
                    </defs>
                    <rect width="150" height="150" fill="url(#scrapingGrad${index})" rx="25"/>
                    <rect x="20" y="30" width="110" height="70" fill="white" fill-opacity="0.15" rx="8"/>
                    <text x="75" y="120" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">${initials}</text>
                    <circle cx="35" cy="45" r="3" fill="white" fill-opacity="0.8"/>
                    <circle cx="50" cy="45" r="3" fill="white" fill-opacity="0.6"/>
                    <circle cx="65" cy="45" r="3" fill="white" fill-opacity="0.4"/>
                    <rect x="30" y="60" width="90" height="2" fill="white" fill-opacity="0.6" rx="1"/>
                    <rect x="30" y="70" width="70" height="2" fill="white" fill-opacity="0.4" rx="1"/>
                    <rect x="30" y="80" width="80" height="2" fill="white" fill-opacity="0.3" rx="1"/>
                  </svg>
                `)}`;
              };

              return (
                <div key={index} className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-2xl p-6 hover:border-green-500/50 transition-all duration-300">
                  <div className="flex items-center mb-4">
                    <img
                      src={getScrapingPlaceholder(review.name, index)}
                      alt={review.name}
                      className="w-12 h-12 rounded-full mr-4"
                    />
                    <div>
                      <h4 className="text-white font-semibold">{review.name}</h4>
                      <p className="text-gray-400 text-sm">{review.role} • {review.company}</p>
                    </div>
                  </div>

                  <div className="flex mb-3">
                    {[...Array(review.rating)].map((_, i) => (
                      <span key={i} className="text-yellow-400 text-lg">★</span>
                    ))}
                  </div>

                  <p className="text-gray-300 text-sm leading-relaxed mb-4">{review.text}</p>

                  <div className="text-xs text-green-400 font-medium">
                    Project: {review.project}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="w-full py-20 bg-black" aria-label="Contact us">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to <span className="text-green-400">Extract Data</span>?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Get in touch with our web scraping experts for a custom solution
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-2 gap-12">
              <div className="space-y-8">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-4">Get Started Today</h3>
                  <p className="text-gray-300 leading-relaxed">
                    Whether you need to extract product data, monitor prices, gather market intelligence,
                    or automate data collection, our team has the expertise to deliver reliable solutions.
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-green-600/20 rounded-lg flex items-center justify-center">
                      <span className="text-green-400 text-xl">📧</span>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Email</p>
                      <button
                        onClick={() => {
                          const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=${encodeURIComponent('Web Scraping Service Inquiry - The Semantics')}&body=${encodeURIComponent('Hello The Semantics Team,\n\nI am interested in your web scraping services.\n\nProject Details:\n- Target Websites: \n- Data Requirements: \n- Timeline: \n- Budget Range: \n\nPlease contact me to discuss further.\n\nThank you!\n\nBest regards')}`;
                          window.open(gmailUrl, '_blank');
                        }}
                        className="text-white hover:text-green-400 transition-colors cursor-pointer text-left"
                      >
                        <EMAIL>
                      </button>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-green-600/20 rounded-lg flex items-center justify-center">
                      <span className="text-green-400 text-xl">🌐</span>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Fiverr Profile</p>
                      <a href="https://www.fiverr.com/tahirccreative" target="_blank" rel="noopener noreferrer" className="text-white hover:text-green-400 transition-colors">
                        View Our Fiverr Profile
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-2xl p-8">
                <h3 className="text-xl font-bold text-white mb-6">Quick Contact</h3>
                <div className="space-y-4">
                  <div className="flex flex-col sm:flex-row gap-4">
                    <button
                      onClick={() => {
                        const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=${encodeURIComponent('Web Scraping Service Inquiry - The Semantics')}&body=${encodeURIComponent('Hello The Semantics Team,\n\nI am interested in your web scraping services and would like to discuss my project requirements.\n\nProject Details:\n- Target Websites: \n- Data Requirements: \n- Timeline: \n- Budget Range: \n- Additional Notes: \n\nPlease contact me to discuss further.\n\nThank you!\n\nBest regards')}`;
                        window.open(gmailUrl, '_blank');
                      }}
                      className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 text-center cursor-pointer"
                    >
                      Send Email
                    </button>
                    <a
                      href="https://www.fiverr.com/tahirccreative"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="border border-gray-600 hover:border-green-400 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 text-center"
                    >
                      Hire on Fiverr
                    </a>
                  </div>

                  <div className="text-center pt-4">
                    <p className="text-gray-400 text-sm">
                      Response time: Usually within 2-4 hours
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default WebScrapingDemo;
