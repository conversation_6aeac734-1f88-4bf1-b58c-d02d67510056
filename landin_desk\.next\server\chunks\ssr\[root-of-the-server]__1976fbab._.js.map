{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/src/components/web-vitals.tsx"], "sourcesContent": ["'use client';\n\nimport { useReportWebVitals } from 'next/web-vitals';\n\nexport function WebVitals() {\n  useReportWebVitals((metric) => {\n    // Log metrics for debugging in development\n    if (process.env.NODE_ENV === 'development') {\n      console.log('Web Vitals:', metric);\n    }\n    \n    // In production, you could send these to an analytics service\n    // Example: analytics.track('Web Vitals', metric);\n  });\n\n  return null;\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIO,SAAS;IACd,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD,EAAE,CAAC;QAClB,2CAA2C;QAC3C,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,eAAe;QAC7B;IAEA,8DAA8D;IAC9D,kDAAkD;IACpD;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/node_modules/next/dist/compiled/web-vitals/web-vitals.js"], "sourcesContent": ["(function(){\"use strict\";var n={};!function(){n.d=function(b,L){for(var P in L){if(n.o(L,P)&&!n.o(b,P)){Object.defineProperty(b,P,{enumerable:true,get:L[P]})}}}}();!function(){n.o=function(n,b){return Object.prototype.hasOwnProperty.call(n,b)}}();!function(){n.r=function(n){if(typeof Symbol!==\"undefined\"&&Symbol.toStringTag){Object.defineProperty(n,Symbol.toStringTag,{value:\"Module\"})}Object.defineProperty(n,\"__esModule\",{value:true})}}();if(typeof n!==\"undefined\")n.ab=__dirname+\"/\";var b={};n.r(b);n.d(b,{CLSThresholds:function(){return j},FCPThresholds:function(){return B},FIDThresholds:function(){return cn},INPThresholds:function(){return nn},LCPThresholds:function(){return en},TTFBThresholds:function(){return rn},onCLS:function(){return w},onFCP:function(){return S},onFID:function(){return $},onINP:function(){return N},onLCP:function(){return z},onTTFB:function(){return K}});var L,P,I,A,F,D=-1,a=function(n){addEventListener(\"pageshow\",(function(b){b.persisted&&(D=b.timeStamp,n(b))}),!0)},c=function(){var n=self.performance&&performance.getEntriesByType&&performance.getEntriesByType(\"navigation\")[0];if(n&&n.responseStart>0&&n.responseStart<performance.now())return n},u=function(){var n=c();return n&&n.activationStart||0},f=function(n,b){var L=c(),P=\"navigate\";D>=0?P=\"back-forward-cache\":L&&(document.prerendering||u()>0?P=\"prerender\":document.wasDiscarded?P=\"restore\":L.type&&(P=L.type.replace(/_/g,\"-\")));return{name:n,value:void 0===b?-1:b,rating:\"good\",delta:0,entries:[],id:\"v4-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:P}},s=function(n,b,L){try{if(PerformanceObserver.supportedEntryTypes.includes(n)){var P=new PerformanceObserver((function(n){Promise.resolve().then((function(){b(n.getEntries())}))}));return P.observe(Object.assign({type:n,buffered:!0},L||{})),P}}catch(n){}},d=function(n,b,L,P){var I,A;return function(F){b.value>=0&&(F||P)&&((A=b.value-(I||0))||void 0===I)&&(I=b.value,b.delta=A,b.rating=function(n,b){return n>b[1]?\"poor\":n>b[0]?\"needs-improvement\":\"good\"}(b.value,L),n(b))}},l=function(n){requestAnimationFrame((function(){return requestAnimationFrame((function(){return n()}))}))},p=function(n){document.addEventListener(\"visibilitychange\",(function(){\"hidden\"===document.visibilityState&&n()}))},v=function(n){var b=!1;return function(){b||(n(),b=!0)}},O=-1,h=function(){return\"hidden\"!==document.visibilityState||document.prerendering?1/0:0},g=function(n){\"hidden\"===document.visibilityState&&O>-1&&(O=\"visibilitychange\"===n.type?n.timeStamp:0,T())},y=function(){addEventListener(\"visibilitychange\",g,!0),addEventListener(\"prerenderingchange\",g,!0)},T=function(){removeEventListener(\"visibilitychange\",g,!0),removeEventListener(\"prerenderingchange\",g,!0)},E=function(){return O<0&&(O=h(),y(),a((function(){setTimeout((function(){O=h(),y()}),0)}))),{get firstHiddenTime(){return O}}},C=function(n){document.prerendering?addEventListener(\"prerenderingchange\",(function(){return n()}),!0):n()},B=[1800,3e3],S=function(n,b){b=b||{},C((function(){var L,P=E(),I=f(\"FCP\"),A=s(\"paint\",(function(n){n.forEach((function(n){\"first-contentful-paint\"===n.name&&(A.disconnect(),n.startTime<P.firstHiddenTime&&(I.value=Math.max(n.startTime-u(),0),I.entries.push(n),L(!0)))}))}));A&&(L=d(n,I,B,b.reportAllChanges),a((function(P){I=f(\"FCP\"),L=d(n,I,B,b.reportAllChanges),l((function(){I.value=performance.now()-P.timeStamp,L(!0)}))})))}))},j=[.1,.25],w=function(n,b){b=b||{},S(v((function(){var L,P=f(\"CLS\",0),I=0,A=[],c=function(n){n.forEach((function(n){if(!n.hadRecentInput){var b=A[0],L=A[A.length-1];I&&n.startTime-L.startTime<1e3&&n.startTime-b.startTime<5e3?(I+=n.value,A.push(n)):(I=n.value,A=[n])}})),I>P.value&&(P.value=I,P.entries=A,L())},F=s(\"layout-shift\",c);F&&(L=d(n,P,j,b.reportAllChanges),p((function(){c(F.takeRecords()),L(!0)})),a((function(){I=0,P=f(\"CLS\",0),L=d(n,P,j,b.reportAllChanges),l((function(){return L()}))})),setTimeout(L,0))})))},x=0,_=1/0,G=0,M=function(n){n.forEach((function(n){n.interactionId&&(_=Math.min(_,n.interactionId),G=Math.max(G,n.interactionId),x=G?(G-_)/7+1:0)}))},k=function(){\"interactionCount\"in performance||L||(L=s(\"event\",M,{type:\"event\",buffered:!0,durationThreshold:0}))},J=[],Q=new Map,U=0,R=function(){return(L?x:performance.interactionCount||0)-U},Z=[],H=function(n){if(Z.forEach((function(b){return b(n)})),n.interactionId||\"first-input\"===n.entryType){var b=J[J.length-1],L=Q.get(n.interactionId);if(L||J.length<10||n.duration>b.latency){if(L)n.duration>L.latency?(L.entries=[n],L.latency=n.duration):n.duration===L.latency&&n.startTime===L.entries[0].startTime&&L.entries.push(n);else{var P={id:n.interactionId,latency:n.duration,entries:[n]};Q.set(P.id,P),J.push(P)}J.sort((function(n,b){return b.latency-n.latency})),J.length>10&&J.splice(10).forEach((function(n){return Q.delete(n.id)}))}}},q=function(n){var b=self.requestIdleCallback||self.setTimeout,L=-1;return n=v(n),\"hidden\"===document.visibilityState?n():(L=b(n),p(n)),L},nn=[200,500],N=function(n,b){\"PerformanceEventTiming\"in self&&\"interactionId\"in PerformanceEventTiming.prototype&&(b=b||{},C((function(){var L;k();var P,I=f(\"INP\"),o=function(n){q((function(){n.forEach(H);var b,L=(b=Math.min(J.length-1,Math.floor(R()/50)),J[b]);L&&L.latency!==I.value&&(I.value=L.latency,I.entries=L.entries,P())}))},A=s(\"event\",o,{durationThreshold:null!==(L=b.durationThreshold)&&void 0!==L?L:40});P=d(n,I,nn,b.reportAllChanges),A&&(A.observe({type:\"first-input\",buffered:!0}),p((function(){o(A.takeRecords()),P(!0)})),a((function(){U=0,J.length=0,Q.clear(),I=f(\"INP\"),P=d(n,I,nn,b.reportAllChanges)})))})))},en=[2500,4e3],tn={},z=function(n,b){b=b||{},C((function(){var L,P=E(),I=f(\"LCP\"),o=function(n){b.reportAllChanges||(n=n.slice(-1)),n.forEach((function(n){n.startTime<P.firstHiddenTime&&(I.value=Math.max(n.startTime-u(),0),I.entries=[n],L())}))},A=s(\"largest-contentful-paint\",o);if(A){L=d(n,I,en,b.reportAllChanges);var F=v((function(){tn[I.id]||(o(A.takeRecords()),A.disconnect(),tn[I.id]=!0,L(!0))}));[\"keydown\",\"click\"].forEach((function(n){addEventListener(n,(function(){return q(F)}),!0)})),p(F),a((function(P){I=f(\"LCP\"),L=d(n,I,en,b.reportAllChanges),l((function(){I.value=performance.now()-P.timeStamp,tn[I.id]=!0,L(!0)}))}))}}))},rn=[800,1800],on=function e(n){document.prerendering?C((function(){return e(n)})):\"complete\"!==document.readyState?addEventListener(\"load\",(function(){return e(n)}),!0):setTimeout(n,0)},K=function(n,b){b=b||{};var L=f(\"TTFB\"),P=d(n,L,rn,b.reportAllChanges);on((function(){var I=c();I&&(L.value=Math.max(I.responseStart-u(),0),L.entries=[I],P(!0),a((function(){L=f(\"TTFB\",0),(P=d(n,L,rn,b.reportAllChanges))(!0)})))}))},an={passive:!0,capture:!0},un=new Date,V=function(n,b){P||(P=b,I=n,A=new Date,Y(removeEventListener),W())},W=function(){if(I>=0&&I<A-un){var n={entryType:\"first-input\",name:P.type,target:P.target,cancelable:P.cancelable,startTime:P.timeStamp,processingStart:P.timeStamp+I};F.forEach((function(b){b(n)})),F=[]}},X=function(n){if(n.cancelable){var b=(n.timeStamp>1e12?new Date:performance.now())-n.timeStamp;\"pointerdown\"==n.type?function(n,b){var t=function(){V(n,b),i()},r=function(){i()},i=function(){removeEventListener(\"pointerup\",t,an),removeEventListener(\"pointercancel\",r,an)};addEventListener(\"pointerup\",t,an),addEventListener(\"pointercancel\",r,an)}(b,n):V(b,n)}},Y=function(n){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(b){return n(b,X,an)}))},cn=[100,300],$=function(n,b){b=b||{},C((function(){var L,A=E(),D=f(\"FID\"),l=function(n){n.startTime<A.firstHiddenTime&&(D.value=n.processingStart-n.startTime,D.entries.push(n),L(!0))},m=function(n){n.forEach(l)},O=s(\"first-input\",m);L=d(n,D,cn,b.reportAllChanges),O&&(p(v((function(){m(O.takeRecords()),O.disconnect()}))),a((function(){var A;D=f(\"FID\"),L=d(n,D,cn,b.reportAllChanges),F=[],I=-1,P=null,Y(addEventListener),A=l,F.push(A),W()})))}))};module.exports=b})();"], "names": [], "mappings": "AAAA,CAAC;IAAW;IAAa,IAAI,IAAE,CAAC;IAAE,CAAC;QAAW,EAAE,CAAC,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,IAAI,IAAI,KAAK,EAAE;gBAAC,IAAG,EAAE,CAAC,CAAC,GAAE,MAAI,CAAC,EAAE,CAAC,CAAC,GAAE,IAAG;oBAAC,OAAO,cAAc,CAAC,GAAE,GAAE;wBAAC,YAAW;wBAAK,KAAI,CAAC,CAAC,EAAE;oBAAA;gBAAE;YAAC;QAAC;IAAC;IAAI,CAAC;QAAW,EAAE,CAAC,GAAC,SAAS,CAAC,EAAC,CAAC;YAAE,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAE;QAAE;IAAC;IAAI,CAAC;QAAW,EAAE,CAAC,GAAC,SAAS,CAAC;YAAE,IAAG,OAAO,WAAS,eAAa,OAAO,WAAW,EAAC;gBAAC,OAAO,cAAc,CAAC,GAAE,OAAO,WAAW,EAAC;oBAAC,OAAM;gBAAQ;YAAE;YAAC,OAAO,cAAc,CAAC,GAAE,cAAa;gBAAC,OAAM;YAAI;QAAE;IAAC;IAAI,IAAG,OAAO,MAAI,aAAY,EAAE,EAAE,GAAC,uFAAU;IAAI,IAAI,IAAE,CAAC;IAAE,EAAE,CAAC,CAAC;IAAG,EAAE,CAAC,CAAC,GAAE;QAAC,eAAc;YAAW,OAAO;QAAC;QAAE,eAAc;YAAW,OAAO;QAAC;QAAE,eAAc;YAAW,OAAO;QAAE;QAAE,eAAc;YAAW,OAAO;QAAE;QAAE,eAAc;YAAW,OAAO;QAAE;QAAE,gBAAe;YAAW,OAAO;QAAE;QAAE,OAAM;YAAW,OAAO;QAAC;QAAE,OAAM;YAAW,OAAO;QAAC;QAAE,OAAM;YAAW,OAAO;QAAC;QAAE,OAAM;YAAW,OAAO;QAAC;QAAE,OAAM;YAAW,OAAO;QAAC;QAAE,QAAO;YAAW,OAAO;QAAC;IAAC;IAAG,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,CAAC,GAAE,IAAE,SAAS,CAAC;QAAE,iBAAiB,YAAY,SAAS,CAAC;YAAE,EAAE,SAAS,IAAE,CAAC,IAAE,EAAE,SAAS,EAAC,EAAE,EAAE;QAAC,GAAG,CAAC;IAAE,GAAE,IAAE;QAAW,IAAI,IAAE,KAAK,WAAW,IAAE,YAAY,gBAAgB,IAAE,YAAY,gBAAgB,CAAC,aAAa,CAAC,EAAE;QAAC,IAAG,KAAG,EAAE,aAAa,GAAC,KAAG,EAAE,aAAa,GAAC,YAAY,GAAG,IAAG,OAAO;IAAC,GAAE,IAAE;QAAW,IAAI,IAAE;QAAI,OAAO,KAAG,EAAE,eAAe,IAAE;IAAC,GAAE,IAAE,SAAS,CAAC,EAAC,CAAC;QAAE,IAAI,IAAE,KAAI,IAAE;QAAW,KAAG,IAAE,IAAE,uBAAqB,KAAG,CAAC,SAAS,YAAY,IAAE,MAAI,IAAE,IAAE,cAAY,SAAS,YAAY,GAAC,IAAE,YAAU,EAAE,IAAI,IAAE,CAAC,IAAE,EAAE,IAAI,CAAC,OAAO,CAAC,MAAK,IAAI,CAAC;QAAE,OAAM;YAAC,MAAK;YAAE,OAAM,KAAK,MAAI,IAAE,CAAC,IAAE;YAAE,QAAO;YAAO,OAAM;YAAE,SAAQ,EAAE;YAAC,IAAG,MAAM,MAAM,CAAC,KAAK,GAAG,IAAG,KAAK,MAAM,CAAC,KAAK,KAAK,CAAC,gBAAc,KAAK,MAAM,MAAI;YAAM,gBAAe;QAAC;IAAC,GAAE,IAAE,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC;QAAE,IAAG;YAAC,IAAG,oBAAoB,mBAAmB,CAAC,QAAQ,CAAC,IAAG;gBAAC,IAAI,IAAE,IAAI,oBAAqB,SAAS,CAAC;oBAAE,QAAQ,OAAO,GAAG,IAAI,CAAE;wBAAW,EAAE,EAAE,UAAU;oBAAG;gBAAG;gBAAI,OAAO,EAAE,OAAO,CAAC,OAAO,MAAM,CAAC;oBAAC,MAAK;oBAAE,UAAS,CAAC;gBAAC,GAAE,KAAG,CAAC,KAAI;YAAC;QAAC,EAAC,OAAM,GAAE,CAAC;IAAC,GAAE,IAAE,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,GAAE;QAAE,OAAO,SAAS,CAAC;YAAE,EAAE,KAAK,IAAE,KAAG,CAAC,KAAG,CAAC,KAAG,CAAC,CAAC,IAAE,EAAE,KAAK,GAAC,CAAC,KAAG,CAAC,CAAC,KAAG,KAAK,MAAI,CAAC,KAAG,CAAC,IAAE,EAAE,KAAK,EAAC,EAAE,KAAK,GAAC,GAAE,EAAE,MAAM,GAAC,SAAS,CAAC,EAAC,CAAC;gBAAE,OAAO,IAAE,CAAC,CAAC,EAAE,GAAC,SAAO,IAAE,CAAC,CAAC,EAAE,GAAC,sBAAoB;YAAM,EAAE,EAAE,KAAK,EAAC,IAAG,EAAE,EAAE;QAAC;IAAC,GAAE,IAAE,SAAS,CAAC;QAAE,sBAAuB;YAAW,OAAO,sBAAuB;gBAAW,OAAO;YAAG;QAAG;IAAG,GAAE,IAAE,SAAS,CAAC;QAAE,SAAS,gBAAgB,CAAC,oBAAoB;YAAW,aAAW,SAAS,eAAe,IAAE;QAAG;IAAG,GAAE,IAAE,SAAS,CAAC;QAAE,IAAI,IAAE,CAAC;QAAE,OAAO;YAAW,KAAG,CAAC,KAAI,IAAE,CAAC,CAAC;QAAC;IAAC,GAAE,IAAE,CAAC,GAAE,IAAE;QAAW,OAAM,aAAW,SAAS,eAAe,IAAE,SAAS,YAAY,GAAC,IAAE,IAAE;IAAC,GAAE,IAAE,SAAS,CAAC;QAAE,aAAW,SAAS,eAAe,IAAE,IAAE,CAAC,KAAG,CAAC,IAAE,uBAAqB,EAAE,IAAI,GAAC,EAAE,SAAS,GAAC,GAAE,GAAG;IAAC,GAAE,IAAE;QAAW,iBAAiB,oBAAmB,GAAE,CAAC,IAAG,iBAAiB,sBAAqB,GAAE,CAAC;IAAE,GAAE,IAAE;QAAW,oBAAoB,oBAAmB,GAAE,CAAC,IAAG,oBAAoB,sBAAqB,GAAE,CAAC;IAAE,GAAE,IAAE;QAAW,OAAO,IAAE,KAAG,CAAC,IAAE,KAAI,KAAI,EAAG;YAAW,WAAY;gBAAW,IAAE,KAAI;YAAG,GAAG;QAAE,EAAG,GAAE;YAAC,IAAI,mBAAiB;gBAAC,OAAO;YAAC;QAAC;IAAC,GAAE,IAAE,SAAS,CAAC;QAAE,SAAS,YAAY,GAAC,iBAAiB,sBAAsB;YAAW,OAAO;QAAG,GAAG,CAAC,KAAG;IAAG,GAAE,IAAE;QAAC;QAAK;KAAI,EAAC,IAAE,SAAS,CAAC,EAAC,CAAC;QAAE,IAAE,KAAG,CAAC,GAAE,EAAG;YAAW,IAAI,GAAE,IAAE,KAAI,IAAE,EAAE,QAAO,IAAE,EAAE,SAAS,SAAS,CAAC;gBAAE,EAAE,OAAO,CAAE,SAAS,CAAC;oBAAE,6BAA2B,EAAE,IAAI,IAAE,CAAC,EAAE,UAAU,IAAG,EAAE,SAAS,GAAC,EAAE,eAAe,IAAE,CAAC,EAAE,KAAK,GAAC,KAAK,GAAG,CAAC,EAAE,SAAS,GAAC,KAAI,IAAG,EAAE,OAAO,CAAC,IAAI,CAAC,IAAG,EAAE,CAAC,EAAE,CAAC;gBAAC;YAAG;YAAI,KAAG,CAAC,IAAE,EAAE,GAAE,GAAE,GAAE,EAAE,gBAAgB,GAAE,EAAG,SAAS,CAAC;gBAAE,IAAE,EAAE,QAAO,IAAE,EAAE,GAAE,GAAE,GAAE,EAAE,gBAAgB,GAAE,EAAG;oBAAW,EAAE,KAAK,GAAC,YAAY,GAAG,KAAG,EAAE,SAAS,EAAC,EAAE,CAAC;gBAAE;YAAG,EAAG;QAAC;IAAG,GAAE,IAAE;QAAC;QAAG;KAAI,EAAC,IAAE,SAAS,CAAC,EAAC,CAAC;QAAE,IAAE,KAAG,CAAC,GAAE,EAAE,EAAG;YAAW,IAAI,GAAE,IAAE,EAAE,OAAM,IAAG,IAAE,GAAE,IAAE,EAAE,EAAC,IAAE,SAAS,CAAC;gBAAE,EAAE,OAAO,CAAE,SAAS,CAAC;oBAAE,IAAG,CAAC,EAAE,cAAc,EAAC;wBAAC,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE;wBAAC,KAAG,EAAE,SAAS,GAAC,EAAE,SAAS,GAAC,OAAK,EAAE,SAAS,GAAC,EAAE,SAAS,GAAC,MAAI,CAAC,KAAG,EAAE,KAAK,EAAC,EAAE,IAAI,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,KAAK,EAAC,IAAE;4BAAC;yBAAE;oBAAC;gBAAC,IAAI,IAAE,EAAE,KAAK,IAAE,CAAC,EAAE,KAAK,GAAC,GAAE,EAAE,OAAO,GAAC,GAAE,GAAG;YAAC,GAAE,IAAE,EAAE,gBAAe;YAAG,KAAG,CAAC,IAAE,EAAE,GAAE,GAAE,GAAE,EAAE,gBAAgB,GAAE,EAAG;gBAAW,EAAE,EAAE,WAAW,KAAI,EAAE,CAAC;YAAE,IAAI,EAAG;gBAAW,IAAE,GAAE,IAAE,EAAE,OAAM,IAAG,IAAE,EAAE,GAAE,GAAE,GAAE,EAAE,gBAAgB,GAAE,EAAG;oBAAW,OAAO;gBAAG;YAAG,IAAI,WAAW,GAAE,EAAE;QAAC;IAAI,GAAE,IAAE,GAAE,IAAE,IAAE,GAAE,IAAE,GAAE,IAAE,SAAS,CAAC;QAAE,EAAE,OAAO,CAAE,SAAS,CAAC;YAAE,EAAE,aAAa,IAAE,CAAC,IAAE,KAAK,GAAG,CAAC,GAAE,EAAE,aAAa,GAAE,IAAE,KAAK,GAAG,CAAC,GAAE,EAAE,aAAa,GAAE,IAAE,IAAE,CAAC,IAAE,CAAC,IAAE,IAAE,IAAE,CAAC;QAAC;IAAG,GAAE,IAAE;QAAW,sBAAqB,eAAa,KAAG,CAAC,IAAE,EAAE,SAAQ,GAAE;YAAC,MAAK;YAAQ,UAAS,CAAC;YAAE,mBAAkB;QAAC,EAAE;IAAC,GAAE,IAAE,EAAE,EAAC,IAAE,IAAI,KAAI,IAAE,GAAE,IAAE;QAAW,OAAM,CAAC,IAAE,IAAE,YAAY,gBAAgB,IAAE,CAAC,IAAE;IAAC,GAAE,IAAE,EAAE,EAAC,IAAE,SAAS,CAAC;QAAE,IAAG,EAAE,OAAO,CAAE,SAAS,CAAC;YAAE,OAAO,EAAE;QAAE,IAAI,EAAE,aAAa,IAAE,kBAAgB,EAAE,SAAS,EAAC;YAAC,IAAI,IAAE,CAAC,CAAC,EAAE,MAAM,GAAC,EAAE,EAAC,IAAE,EAAE,GAAG,CAAC,EAAE,aAAa;YAAE,IAAG,KAAG,EAAE,MAAM,GAAC,MAAI,EAAE,QAAQ,GAAC,EAAE,OAAO,EAAC;gBAAC,IAAG,GAAE,EAAE,QAAQ,GAAC,EAAE,OAAO,GAAC,CAAC,EAAE,OAAO,GAAC;oBAAC;iBAAE,EAAC,EAAE,OAAO,GAAC,EAAE,QAAQ,IAAE,EAAE,QAAQ,KAAG,EAAE,OAAO,IAAE,EAAE,SAAS,KAAG,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,IAAE,EAAE,OAAO,CAAC,IAAI,CAAC;qBAAO;oBAAC,IAAI,IAAE;wBAAC,IAAG,EAAE,aAAa;wBAAC,SAAQ,EAAE,QAAQ;wBAAC,SAAQ;4BAAC;yBAAE;oBAAA;oBAAE,EAAE,GAAG,CAAC,EAAE,EAAE,EAAC,IAAG,EAAE,IAAI,CAAC;gBAAE;gBAAC,EAAE,IAAI,CAAE,SAAS,CAAC,EAAC,CAAC;oBAAE,OAAO,EAAE,OAAO,GAAC,EAAE,OAAO;gBAAA,IAAI,EAAE,MAAM,GAAC,MAAI,EAAE,MAAM,CAAC,IAAI,OAAO,CAAE,SAAS,CAAC;oBAAE,OAAO,EAAE,MAAM,CAAC,EAAE,EAAE;gBAAC;YAAG;QAAC;IAAC,GAAE,IAAE,SAAS,CAAC;QAAE,IAAI,IAAE,KAAK,mBAAmB,IAAE,KAAK,UAAU,EAAC,IAAE,CAAC;QAAE,OAAO,IAAE,EAAE,IAAG,aAAW,SAAS,eAAe,GAAC,MAAI,CAAC,IAAE,EAAE,IAAG,EAAE,EAAE,GAAE;IAAC,GAAE,KAAG;QAAC;QAAI;KAAI,EAAC,IAAE,SAAS,CAAC,EAAC,CAAC;QAAE,4BAA2B,QAAM,mBAAkB,uBAAuB,SAAS,IAAE,CAAC,IAAE,KAAG,CAAC,GAAE,EAAG;YAAW,IAAI;YAAE;YAAI,IAAI,GAAE,IAAE,EAAE,QAAO,IAAE,SAAS,CAAC;gBAAE,EAAG;oBAAW,EAAE,OAAO,CAAC;oBAAG,IAAI,GAAE,IAAE,CAAC,IAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAC,GAAE,KAAK,KAAK,CAAC,MAAI,MAAK,CAAC,CAAC,EAAE;oBAAE,KAAG,EAAE,OAAO,KAAG,EAAE,KAAK,IAAE,CAAC,EAAE,KAAK,GAAC,EAAE,OAAO,EAAC,EAAE,OAAO,GAAC,EAAE,OAAO,EAAC,GAAG;gBAAC;YAAG,GAAE,IAAE,EAAE,SAAQ,GAAE;gBAAC,mBAAkB,SAAO,CAAC,IAAE,EAAE,iBAAiB,KAAG,KAAK,MAAI,IAAE,IAAE;YAAE;YAAG,IAAE,EAAE,GAAE,GAAE,IAAG,EAAE,gBAAgB,GAAE,KAAG,CAAC,EAAE,OAAO,CAAC;gBAAC,MAAK;gBAAc,UAAS,CAAC;YAAC,IAAG,EAAG;gBAAW,EAAE,EAAE,WAAW,KAAI,EAAE,CAAC;YAAE,IAAI,EAAG;gBAAW,IAAE,GAAE,EAAE,MAAM,GAAC,GAAE,EAAE,KAAK,IAAG,IAAE,EAAE,QAAO,IAAE,EAAE,GAAE,GAAE,IAAG,EAAE,gBAAgB;YAAC,EAAG;QAAC,EAAG;IAAC,GAAE,KAAG;QAAC;QAAK;KAAI,EAAC,KAAG,CAAC,GAAE,IAAE,SAAS,CAAC,EAAC,CAAC;QAAE,IAAE,KAAG,CAAC,GAAE,EAAG;YAAW,IAAI,GAAE,IAAE,KAAI,IAAE,EAAE,QAAO,IAAE,SAAS,CAAC;gBAAE,EAAE,gBAAgB,IAAE,CAAC,IAAE,EAAE,KAAK,CAAC,CAAC,EAAE,GAAE,EAAE,OAAO,CAAE,SAAS,CAAC;oBAAE,EAAE,SAAS,GAAC,EAAE,eAAe,IAAE,CAAC,EAAE,KAAK,GAAC,KAAK,GAAG,CAAC,EAAE,SAAS,GAAC,KAAI,IAAG,EAAE,OAAO,GAAC;wBAAC;qBAAE,EAAC,GAAG;gBAAC;YAAG,GAAE,IAAE,EAAE,4BAA2B;YAAG,IAAG,GAAE;gBAAC,IAAE,EAAE,GAAE,GAAE,IAAG,EAAE,gBAAgB;gBAAE,IAAI,IAAE,EAAG;oBAAW,EAAE,CAAC,EAAE,EAAE,CAAC,IAAE,CAAC,EAAE,EAAE,WAAW,KAAI,EAAE,UAAU,IAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAC,CAAC,GAAE,EAAE,CAAC,EAAE;gBAAC;gBAAI;oBAAC;oBAAU;iBAAQ,CAAC,OAAO,CAAE,SAAS,CAAC;oBAAE,iBAAiB,GAAG;wBAAW,OAAO,EAAE;oBAAE,GAAG,CAAC;gBAAE,IAAI,EAAE,IAAG,EAAG,SAAS,CAAC;oBAAE,IAAE,EAAE,QAAO,IAAE,EAAE,GAAE,GAAE,IAAG,EAAE,gBAAgB,GAAE,EAAG;wBAAW,EAAE,KAAK,GAAC,YAAY,GAAG,KAAG,EAAE,SAAS,EAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAC,CAAC,GAAE,EAAE,CAAC;oBAAE;gBAAG;YAAG;QAAC;IAAG,GAAE,KAAG;QAAC;QAAI;KAAK,EAAC,KAAG,SAAS,EAAE,CAAC;QAAE,SAAS,YAAY,GAAC,EAAG;YAAW,OAAO,EAAE;QAAE,KAAI,eAAa,SAAS,UAAU,GAAC,iBAAiB,QAAQ;YAAW,OAAO,EAAE;QAAE,GAAG,CAAC,KAAG,WAAW,GAAE;IAAE,GAAE,IAAE,SAAS,CAAC,EAAC,CAAC;QAAE,IAAE,KAAG,CAAC;QAAE,IAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,GAAE,GAAE,IAAG,EAAE,gBAAgB;QAAE,GAAI;YAAW,IAAI,IAAE;YAAI,KAAG,CAAC,EAAE,KAAK,GAAC,KAAK,GAAG,CAAC,EAAE,aAAa,GAAC,KAAI,IAAG,EAAE,OAAO,GAAC;gBAAC;aAAE,EAAC,EAAE,CAAC,IAAG,EAAG;gBAAW,IAAE,EAAE,QAAO,IAAG,CAAC,IAAE,EAAE,GAAE,GAAE,IAAG,EAAE,gBAAgB,CAAC,EAAE,CAAC;YAAE,EAAG;QAAC;IAAG,GAAE,KAAG;QAAC,SAAQ,CAAC;QAAE,SAAQ,CAAC;IAAC,GAAE,KAAG,IAAI,MAAK,IAAE,SAAS,CAAC,EAAC,CAAC;QAAE,KAAG,CAAC,IAAE,GAAE,IAAE,GAAE,IAAE,IAAI,MAAK,EAAE,sBAAqB,GAAG;IAAC,GAAE,IAAE;QAAW,IAAG,KAAG,KAAG,IAAE,IAAE,IAAG;YAAC,IAAI,IAAE;gBAAC,WAAU;gBAAc,MAAK,EAAE,IAAI;gBAAC,QAAO,EAAE,MAAM;gBAAC,YAAW,EAAE,UAAU;gBAAC,WAAU,EAAE,SAAS;gBAAC,iBAAgB,EAAE,SAAS,GAAC;YAAC;YAAE,EAAE,OAAO,CAAE,SAAS,CAAC;gBAAE,EAAE;YAAE,IAAI,IAAE,EAAE;QAAA;IAAC,GAAE,IAAE,SAAS,CAAC;QAAE,IAAG,EAAE,UAAU,EAAC;YAAC,IAAI,IAAE,CAAC,EAAE,SAAS,GAAC,OAAK,IAAI,OAAK,YAAY,GAAG,EAAE,IAAE,EAAE,SAAS;YAAC,iBAAe,EAAE,IAAI,GAAC,SAAS,CAAC,EAAC,CAAC;gBAAE,IAAI,IAAE;oBAAW,EAAE,GAAE,IAAG;gBAAG,GAAE,IAAE;oBAAW;gBAAG,GAAE,IAAE;oBAAW,oBAAoB,aAAY,GAAE,KAAI,oBAAoB,iBAAgB,GAAE;gBAAG;gBAAE,iBAAiB,aAAY,GAAE,KAAI,iBAAiB,iBAAgB,GAAE;YAAG,EAAE,GAAE,KAAG,EAAE,GAAE;QAAE;IAAC,GAAE,IAAE,SAAS,CAAC;QAAE;YAAC;YAAY;YAAU;YAAa;SAAc,CAAC,OAAO,CAAE,SAAS,CAAC;YAAE,OAAO,EAAE,GAAE,GAAE;QAAG;IAAG,GAAE,KAAG;QAAC;QAAI;KAAI,EAAC,IAAE,SAAS,CAAC,EAAC,CAAC;QAAE,IAAE,KAAG,CAAC,GAAE,EAAG;YAAW,IAAI,GAAE,IAAE,KAAI,IAAE,EAAE,QAAO,IAAE,SAAS,CAAC;gBAAE,EAAE,SAAS,GAAC,EAAE,eAAe,IAAE,CAAC,EAAE,KAAK,GAAC,EAAE,eAAe,GAAC,EAAE,SAAS,EAAC,EAAE,OAAO,CAAC,IAAI,CAAC,IAAG,EAAE,CAAC,EAAE;YAAC,GAAE,IAAE,SAAS,CAAC;gBAAE,EAAE,OAAO,CAAC;YAAE,GAAE,IAAE,EAAE,eAAc;YAAG,IAAE,EAAE,GAAE,GAAE,IAAG,EAAE,gBAAgB,GAAE,KAAG,CAAC,EAAE,EAAG;gBAAW,EAAE,EAAE,WAAW,KAAI,EAAE,UAAU;YAAE,KAAK,EAAG;gBAAW,IAAI;gBAAE,IAAE,EAAE,QAAO,IAAE,EAAE,GAAE,GAAE,IAAG,EAAE,gBAAgB,GAAE,IAAE,EAAE,EAAC,IAAE,CAAC,GAAE,IAAE,MAAK,EAAE,mBAAkB,IAAE,GAAE,EAAE,IAAI,CAAC,IAAG;YAAG,EAAG;QAAC;IAAG;IAAE,OAAO,OAAO,GAAC;AAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/node_modules/next/src/client/web-vitals.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport {\n  onLCP,\n  onFID,\n  onCLS,\n  onINP,\n  onFCP,\n  onTTFB,\n} from 'next/dist/compiled/web-vitals'\nimport type { Metric } from 'next/dist/compiled/web-vitals'\n\nexport function useReportWebVitals(\n  reportWebVitalsFn: (metric: Metric) => void\n) {\n  useEffect(() => {\n    onCLS(reportWebVitalsFn)\n    onFID(reportWebVitalsFn)\n    onLCP(reportWebVitalsFn)\n    onINP(reportWebVitalsFn)\n    onFCP(reportWebVitalsFn)\n    onTTFB(reportWebVitalsFn)\n  }, [reportWebVitalsFn])\n}\n"], "names": ["useReportWebVitals", "reportWebVitalsFn", "useEffect", "onCLS", "onFID", "onLCP", "onINP", "onFCP", "onTTFB"], "mappings": ";;;+BAWgBA,sBAAAA;;;eAAAA;;;uBAXU;2BAQnB;AAGA,SAASA,mBACdC,iBAA2C;IAE3CC,CAAAA,GAAAA,OAAAA,SAAS,EAAC;QACRC,CAAAA,GAAAA,WAAAA,KAAK,EAACF;QACNG,CAAAA,GAAAA,WAAAA,KAAK,EAACH;QACNI,CAAAA,GAAAA,WAAAA,KAAK,EAACJ;QACNK,CAAAA,GAAAA,WAAAA,KAAK,EAACL;QACNM,CAAAA,GAAAA,WAAAA,KAAK,EAACN;QACNO,CAAAA,GAAAA,WAAAA,MAAM,EAACP;IACT,GAAG;QAACA;KAAkB;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 475, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/node_modules/next/web-vitals.js"], "sourcesContent": ["module.exports = require('./dist/client/web-vitals')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}