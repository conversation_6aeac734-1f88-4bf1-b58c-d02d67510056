{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/src/app/loading.tsx"], "sourcesContent": ["export default function Loading() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-[#000] to-[#1A2428] flex items-center justify-center\">\n      <div className=\"text-white/50 text-center\">\n        <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-white/30 mx-auto mb-6\"></div>\n        <h1 className=\"text-2xl font-semibold mb-2\">Loading The Semantics</h1>\n        <p className=\"text-lg\">Preparing your desktop development experience...</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAG,WAAU;8BAA8B;;;;;;8BAC5C,8OAAC;oBAAE,WAAU;8BAAU;;;;;;;;;;;;;;;;;AAI/B", "debugId": null}}]}