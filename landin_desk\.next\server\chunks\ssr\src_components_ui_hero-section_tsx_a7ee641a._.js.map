{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/src/components/ui/hero-section.tsx"], "sourcesContent": ["import React, { useRef } from 'react';\nimport { <PERSON><PERSON>, use<PERSON>rame, useLoader } from '@react-three/fiber';\nimport { TextureLoader, Shape, ExtrudeGeometry } from 'three';\n\nconst Box = ({ position, rotation }: { position: [number, number, number], rotation: [number, number, number] }) => {\n    const shape = new Shape();\n    const angleStep = Math.PI * 0.5;\n    const radius = 1;\n\n    shape.absarc(2, 2, radius, angleStep * 0, angleStep * 1);\n    shape.absarc(-2, 2, radius, angleStep * 1, angleStep * 2);\n    shape.absarc(-2, -2, radius, angleStep * 2, angleStep * 3);\n    shape.absarc(2, -2, radius, angleStep * 3, angleStep * 4);\n\n    const extrudeSettings = {\n        depth: 0.3,\n        bevelEnabled: true,\n        bevelThickness: 0.05,\n        bevelSize: 0.05,\n        bevelSegments: 20,\n        curveSegments: 20\n    };\n\n    const geometry = new ExtrudeGeometry(shape, extrudeSettings);\n    geometry.center();\n\n    return (\n        <mesh\n            geometry={geometry}\n            position={position}\n            rotation={rotation}\n        >\n            <meshPhysicalMaterial \n                color=\"#232323\"\n                metalness={1}\n                roughness={0.3}\n                reflectivity={0.5}\n                ior={1.5}\n                emissive=\"#000000\"\n                emissiveIntensity={0}\n                transparent={false}\n                opacity={1.0}\n                transmission={0.0}\n                thickness={0.5}\n                clearcoat={0.0}\n                clearcoatRoughness={0.0}\n                sheen={0}\n                sheenRoughness={1.0}\n                sheenColor=\"#ffffff\"\n                specularIntensity={1.0}\n                specularColor=\"#ffffff\"\n                iridescence={1}\n                iridescenceIOR={1.3}\n                iridescenceThicknessRange={[100, 400]}\n                flatShading={false}\n                side={0}\n                alphaTest={0}\n                depthWrite={true}\n                depthTest={true}\n            />\n        </mesh>\n    );\n};\n\nconst AnimatedBoxes = () => {\n    const groupRef = useRef<THREE.Group>(null);\n\n    useFrame((state, delta) => {\n        if (groupRef.current) {\n            groupRef.current.rotation.x += delta * 0.05;\n        }\n    });\n\n    const boxes = Array.from({ length: 50 }, (_, index) => ({\n        position: [(index - 25) * 0.75, 0, 0] as [number, number, number],\n        rotation: [\n            (index - 10) * 0.1,\n            Math.PI / 2,\n            0\n        ] as [number, number, number],\n        id: index\n    }));\n\n    return (\n        <group ref={groupRef}>\n            {boxes.map((box) => (\n                <Box\n                    key={box.id}\n                    position={box.position}\n                    rotation={box.rotation}\n                />\n            ))}\n        </group>\n    );\n};\n\nexport const Scene = () => {\n    const [cameraPosition] = React.useState<[number, number, number]>([5, 5, 20]);\n\n    return (\n        <div className=\"w-full h-full z-0\">\n            <Canvas camera={{ position: cameraPosition, fov: 40 }}>\n                <ambientLight intensity={15} />\n                <directionalLight position={[10, 10, 5]} intensity={15} />\n                <AnimatedBoxes />\n            </Canvas>\n        </div>\n    );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;;;;;AAEA,MAAM,MAAM,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAA8E;IAC3G,MAAM,QAAQ,IAAI,+IAAA,CAAA,QAAK;IACvB,MAAM,YAAY,KAAK,EAAE,GAAG;IAC5B,MAAM,SAAS;IAEf,MAAM,MAAM,CAAC,GAAG,GAAG,QAAQ,YAAY,GAAG,YAAY;IACtD,MAAM,MAAM,CAAC,CAAC,GAAG,GAAG,QAAQ,YAAY,GAAG,YAAY;IACvD,MAAM,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,YAAY,GAAG,YAAY;IACxD,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,QAAQ,YAAY,GAAG,YAAY;IAEvD,MAAM,kBAAkB;QACpB,OAAO;QACP,cAAc;QACd,gBAAgB;QAChB,WAAW;QACX,eAAe;QACf,eAAe;IACnB;IAEA,MAAM,WAAW,IAAI,+IAAA,CAAA,kBAAe,CAAC,OAAO;IAC5C,SAAS,MAAM;IAEf,qBACI,8OAAC;QACG,UAAU;QACV,UAAU;QACV,UAAU;kBAEV,cAAA,8OAAC;YACG,OAAM;YACN,WAAW;YACX,WAAW;YACX,cAAc;YACd,KAAK;YACL,UAAS;YACT,mBAAmB;YACnB,aAAa;YACb,SAAS;YACT,cAAc;YACd,WAAW;YACX,WAAW;YACX,oBAAoB;YACpB,OAAO;YACP,gBAAgB;YAChB,YAAW;YACX,mBAAmB;YACnB,eAAc;YACd,aAAa;YACb,gBAAgB;YAChB,2BAA2B;gBAAC;gBAAK;aAAI;YACrC,aAAa;YACb,MAAM;YACN,WAAW;YACX,YAAY;YACZ,WAAW;;;;;;;;;;;AAI3B;AAEA,MAAM,gBAAgB;IAClB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IAErC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,OAAO;QACb,IAAI,SAAS,OAAO,EAAE;YAClB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,QAAQ;QAC3C;IACJ;IAEA,MAAM,QAAQ,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,QAAU,CAAC;YACpD,UAAU;gBAAC,CAAC,QAAQ,EAAE,IAAI;gBAAM;gBAAG;aAAE;YACrC,UAAU;gBACN,CAAC,QAAQ,EAAE,IAAI;gBACf,KAAK,EAAE,GAAG;gBACV;aACH;YACD,IAAI;QACR,CAAC;IAED,qBACI,8OAAC;QAAM,KAAK;kBACP,MAAM,GAAG,CAAC,CAAC,oBACR,8OAAC;gBAEG,UAAU,IAAI,QAAQ;gBACtB,UAAU,IAAI,QAAQ;eAFjB,IAAI,EAAE;;;;;;;;;;AAO/B;AAEO,MAAM,QAAQ;IACjB,MAAM,CAAC,eAAe,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAA2B;QAAC;QAAG;QAAG;KAAG;IAE5E,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC,mMAAA,CAAA,SAAM;YAAC,QAAQ;gBAAE,UAAU;gBAAgB,KAAK;YAAG;;8BAChD,8OAAC;oBAAa,WAAW;;;;;;8BACzB,8OAAC;oBAAiB,UAAU;wBAAC;wBAAI;wBAAI;qBAAE;oBAAE,WAAW;;;;;;8BACpD,8OAAC;;;;;;;;;;;;;;;;AAIjB", "debugId": null}}]}