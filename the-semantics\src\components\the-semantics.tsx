'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { SplineScene } from "@/components/ui/spline"
import { Card } from "@/components/ui/card"
import { CommentSwapper } from "@/components/ui/comment-swapper"
import { X, Code, Zap, Database, Globe, Rocket, Star, Menu, MessageSquare, Box, Users, Award, Clock, Target, Github, Linkedin, Twitter } from 'lucide-react'
import { useInView } from 'react-intersection-observer'

interface TechInfo {
  id: string
  name: string
  title: string
  description: string
  features: string[]
  color: string
  icon: React.ReactNode
}

// Generate unique AI/tech-themed placeholder avatar
const getAIPlaceholder = (name: string, index: number) => {
  const colors = ['#3B82F6', '#8B5CF6', '#06B6D4', '#10B981'];
  const color = colors[index % colors.length];
  const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="150" height="150" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="aiGrad${index}" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${color}50;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="150" height="150" fill="url(#aiGrad${index})" rx="20"/>
      <polygon points="75,25 95,45 75,65 55,45" fill="white" fill-opacity="0.3"/>
      <circle cx="75" cy="45" r="8" fill="white" fill-opacity="0.6"/>
      <text x="75" y="110" font-family="Arial, sans-serif" font-size="28" font-weight="bold" text-anchor="middle" fill="white">${initials}</text>
      <rect x="30" y="80" width="90" height="2" fill="white" fill-opacity="0.4" rx="1"/>
      <rect x="40" y="90" width="70" height="2" fill="white" fill-opacity="0.3" rx="1"/>
    </svg>
  `)}`;
};

const sampleComments = [
  {
    id: "1",
    name: "Aaron Starrett",
    rating: 5,
    comment: "Tahir Siddique exceeded expectations with his professionalism and well-documented software solutions. Working with him was seamless due to his proactive communication and fluency, not to mention he always delivered on time. HIGHLY recommended!",
    avatar: getAIPlaceholder("Aaron Starrett", 0),
    date: "2 days ago"
  },
  {
    id: "2",
    name: "King Co",
    rating: 5,
    comment: "Wow, Tahir is amazing. I have tried to work with a few developers on Fiverr and none of them ever proactively went deep enough with me to truly understand the scope of the project. Tahir exceeded my expectations and will be working with him in the future!!",
    avatar: getAIPlaceholder("King Co", 1),
    date: "1 week ago"
  },
  {
    id: "3",
    name: "Yunik Design",
    rating: 5,
    comment: "I had the pleasure of working with Tahir, and I must say, he truly exceeded all my expectations. His documentation was clear and comprehensive, reflecting his deep understanding and expertise in code. Every detail was meticulously attended to, ensuring the final product was completely bug-free.",
    avatar: getAIPlaceholder("Yunik Design", 2),
    date: "3 days ago"
  },
  {
    id: "4",
    name: "Thibault Armand",
    rating: 5,
    comment: "I had the pleasure to hire Tahir for a data scrapping work. His understanding of the problem, ability to propose solutions and deliver them were outstanding. Very professional and knowledgeable! He understood my project perfectly and created exactly what I needed.",
    avatar: getAIPlaceholder("Thibault Armand", 3),
    date: "1 week ago"
  }
];

const techData: TechInfo[] = [
  {
    id: 'flask',
    name: 'Flask',
    title: 'Flask - Lightweight & Flexible',
    description: 'Flask is a micro web framework written in Python. It\'s designed to make getting started quick and easy, with the ability to scale up to complex applications.',
    features: [
      'Rapid prototyping',
      'Small to medium applications',
      'RESTful APIs',
      'Microservices architecture'
    ],
    color: 'from-emerald-400 to-cyan-400',
    icon: <Code className="w-6 h-6" />
  },
  {
    id: 'fastapi',
    name: 'FastAPI',
    title: 'FastAPI - Modern & High Performance',
    description: 'FastAPI is a modern, fast (high-performance), web framework for building APIs with Python 3.7+ based on standard Python type hints.',
    features: [
      'Automatic API documentation',
      'High performance (comparable to NodeJS)',
      'Type hints support',
      'Async/await support'
    ],
    color: 'from-blue-400 to-purple-400',
    icon: <Zap className="w-6 h-6" />
  },
  {
    id: 'django',
    name: 'Django',
    title: 'Django - The Web Framework for Perfectionists',
    description: 'Django is a high-level Python web framework that encourages rapid development and clean, pragmatic design. Built by experienced developers.',
    features: [
      'Built-in admin interface',
      'ORM (Object-Relational Mapping)',
      'Authentication system',
      'Scalable architecture'
    ],
    color: 'from-purple-400 to-pink-400',
    icon: <Database className="w-6 h-6" />
  }
]

export function TheSemantics() {
  const [selectedTech, setSelectedTech] = useState<TechInfo | null>(null)

  const handleTechClick = (tech: TechInfo) => {
    setSelectedTech(tech)
  }

  const closeModal = () => {
    setSelectedTech(null)
  }

  return (
    <div className="min-h-full bg-black text-white relative">
      {/* Background - Full Page Coverage */}
      <div className="fixed inset-0 w-full h-full z-0 bg-gradient-to-br from-gray-900 via-black to-gray-800" aria-hidden="true">
      </div>

      {/* Navigation */}
      <nav className="absolute top-0 left-0 right-0 z-20 p-6" aria-label="Main navigation">
        <div className="flex justify-between items-center">
          <div className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400">
            The Semantics
          </div>
          <div className="flex gap-4">
            <a
              href="/reviews"
              className="flex items-center gap-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-300 text-white text-sm"
              aria-label="View client reviews"
            >
              <MessageSquare className="w-4 h-4" aria-hidden="true" />
              Reviews
            </a>
            <a
              href="mailto:<EMAIL>"
              className="flex items-center gap-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-300 text-white text-sm"
              aria-label="Contact The Semantics"
            >
              <Globe className="w-4 h-4" aria-hidden="true" />
              Contact
            </a>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10">
        {/* Hero Section */}
        <section className="min-h-screen flex items-center justify-center px-8" aria-label="Hero section">
          <Card className="w-full max-w-7xl h-[80vh] bg-black/20 backdrop-blur-xl border-white/10 relative overflow-hidden">

            <div className="flex h-full">
              {/* Left Content */}
              <div className="flex-1 p-12 relative z-10 flex flex-col justify-center">
                <motion.div
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                >
                  <h1 className="text-6xl md:text-7xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white via-blue-200 to-purple-200 mb-6">
                    The Semantics
                  </h1>
                  <h2 className="text-2xl md:text-3xl font-semibold text-blue-400 mb-4">
                    AI & Technology Solutions
                  </h2>
                  <p className="text-xl text-gray-300 max-w-lg mb-8 leading-relaxed">
                    Leading AI and technology solutions provider. Expert LangChain RAG, machine learning, data science, and semantic AI development. Transform your business with cutting-edge artificial intelligence.
                  </p>
                  
                  <div className="flex gap-6 mb-12">
                    <button
                      onClick={() => {
                        const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=${encodeURIComponent('Start New Project - The Semantics')}&body=${encodeURIComponent('Hello The Semantics Team,\n\nI am interested in starting a new project with your team.\n\nProject Details:\n- Service Type: \n- Requirements: \n- Timeline: \n- Budget Range: \n\nPlease contact me to discuss further.\n\nThank you!\n\nBest regards')}`;
                        window.open(gmailUrl, '_blank');
                      }}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold transition-colors duration-200 flex items-center cursor-pointer"
                    >
                      <Rocket className="w-5 h-5 mr-2" />
                      Start Your Project
                    </button>
                    <button
                      onClick={() => {
                        const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=${encodeURIComponent('Portfolio Inquiry - The Semantics')}&body=${encodeURIComponent('Hello The Semantics Team,\n\nI would like to learn more about your portfolio and services.\n\nInterested in:\n- Service Type: \n- Project Scope: \n- Timeline: \n\nPlease share your portfolio and contact me.\n\nThank you!\n\nBest regards')}`;
                        window.open(gmailUrl, '_blank');
                      }}
                      className="border border-gray-600 hover:border-gray-400 text-white px-8 py-4 rounded-lg font-semibold transition-colors duration-200 flex items-center cursor-pointer"
                    >
                      <Globe className="w-5 h-5 mr-2" />
                      View Portfolio
                    </button>
                  </div>

                  {/* Tech Options */}
                  <div className="space-y-4">
                    <p className="text-gray-400 text-sm uppercase tracking-wider">Choose Your Framework</p>
                    <div className="flex flex-wrap gap-4">
                      {techData.map((tech, index) => (
                        <motion.button
                          key={tech.id}
                          initial={{ opacity: 0, x: -50 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.6, delay: index * 0.2 }}
                          whileHover={{ scale: 1.05, y: -5 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => handleTechClick(tech)}
                          className={`px-6 py-3 rounded-full bg-gradient-to-r ${tech.color} text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2`}
                        >
                          {tech.icon}
                          {tech.name}
                        </motion.button>
                      ))}
                    </div>
                  </div>
                </motion.div>
              </div>

              {/* Right Content - 3D Scene */}
              <div className="flex-1 relative">
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 1, delay: 0.5 }}
                  className="w-full h-full"
                >
                  <SplineScene 
                    scene="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
                    className="w-full h-full"
                  />
                </motion.div>
              </div>
            </div>
          </Card>
        </section>

        {/* Features Section */}
        <section className="py-20 px-8">
          <div className="max-w-6xl mx-auto bg-black/30 backdrop-blur-lg rounded-3xl p-12 border border-white/10">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400 mb-4">
                Why Choose The Semantics?
              </h2>
              <p className="text-gray-400 text-lg max-w-2xl mx-auto">
                We deliver cutting-edge solutions that combine performance, scalability, and modern design principles.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  icon: <Zap className="w-8 h-8" />,
                  title: "Lightning Fast APIs",
                  description: "Build high-performance REST APIs with modern async capabilities and optimized response times."
                },
                {
                  icon: <Database className="w-8 h-8" />,
                  title: "Scalable Architecture",
                  description: "Enterprise-grade applications that grow with your business needs and handle millions of requests."
                },
                {
                  icon: <Star className="w-8 h-8" />,
                  title: "Modern Solutions",
                  description: "Cutting-edge frameworks and tools that keep you ahead of the competition."
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  whileHover={{ y: -10 }}
                  className="p-8 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-300"
                >
                  <div className="text-blue-400 mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-3 text-white">{feature.title}</h3>
                  <p className="text-gray-400 leading-relaxed">{feature.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* About Section */}
        <section className="py-20 bg-gradient-to-b from-gray-900 to-black">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ staggerChildren: 0.2, duration: 0.6 }}
              viewport={{ once: true }}
            >
              {/* Header */}
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
                className="text-center mb-16"
              >
                <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent">
                  About Us
                </h2>
                <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                  We're a passionate team of developers, designers, and strategists
                  dedicated to creating exceptional digital experiences that drive business growth.
                </p>
              </motion.div>

              {/* Stats */}
              <motion.div
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ staggerChildren: 0.2, duration: 0.6 }}
                className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20"
              >
                {[
                  { icon: Users, number: "300+", label: "Happy Clients" },
                  { icon: Award, number: "4.9/5", label: "Client Rating" },
                  { icon: Clock, number: "6+", label: "Years Experience" },
                  { icon: Target, number: "500+", label: "Projects Delivered" },
                ].map((stat, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, ease: "easeOut" }}
                    className="text-center group"
                  >
                    <div className="inline-flex p-4 rounded-xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/10 mb-4 group-hover:scale-110 transition-transform duration-300">
                      <stat.icon className="w-8 h-8 text-blue-400" />
                    </div>
                    <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                      {stat.number}
                    </div>
                    <div className="text-gray-400 font-medium">
                      {stat.label}
                    </div>
                  </motion.div>
                ))}
              </motion.div>

              {/* Company Story */}
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
                className="grid md:grid-cols-2 gap-12 items-center mb-20"
              >
                <div>
                  <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
                    Our Story
                  </h3>
                  <div className="space-y-4 text-gray-300 leading-relaxed">
                    <p>
                      Founded in 2018, we started as a small team of passionate developers
                      who believed that technology could transform businesses and
                      improve people's lives.
                    </p>
                    <p>
                      Today, we've grown into a full-service development agency,
                      helping startups and enterprises build world-class applications
                      that users love and businesses depend on.
                    </p>
                    <p>
                      Our commitment to quality, innovation, and client success has made us
                      a trusted partner for companies looking to make their mark in the digital world.
                    </p>
                  </div>
                </div>
                <div className="relative">
                  <div className="aspect-square rounded-2xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/10 p-8 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-6xl mb-4">🚀</div>
                      <div className="text-2xl font-bold text-white mb-2">Innovation First</div>
                      <div className="text-gray-400">Pushing boundaries in development</div>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Team */}
              <motion.div
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ staggerChildren: 0.2, duration: 0.6 }}
              >
                <motion.div
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, ease: "easeOut" }}
                  className="text-center mb-12"
                >
                  <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
                    Meet Our Team
                  </h3>
                  <p className="text-gray-300 max-w-2xl mx-auto">
                    The talented individuals behind our success, each bringing unique expertise
                    and passion to every project.
                  </p>
                </motion.div>

                <div className="grid md:grid-cols-3 gap-8">
                  {[
                    {
                      name: "Umar Javed",
                      role: "Founder & CEO",
                      image: "/umar-javed.jpg",
                      bio: "Expert in modern web technologies, AI integration, and scalable application architecture. Specializes in React, Node.js, and machine learning implementations.",
                      social: { github: "https://github.com/thesemantics", linkedin: "https://www.linkedin.com/company/thesemanticsco/", twitter: "https://twitter.com/thesemantics" }
                    },
                    {
                      name: "Tahir Siddique",
                      role: "Co-Founder & Full Stack Developer",
                      image: "/tahir-siddique.jpg",
                      bio: "Full stack developer specializing in Python, web scraping, and automation solutions. Expert in creating scalable web applications with a 4.9/5 rating on Fiverr.",
                      social: { github: "https://github.com/tahirccreative", linkedin: "https://www.linkedin.com/in/tahirccreative/", twitter: "https://twitter.com/tahirccreative" }
                    },
                    {
                      name: "James Mitchell",
                      role: "UI/UX Designer & Frontend Specialist",
                      image: "https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=300&h=300&fit=crop&crop=face",
                      bio: "Creative designer with a passion for crafting intuitive user experiences. Combines aesthetic design with functional frontend development.",
                      social: { github: "https://github.com/thesemantics", linkedin: "https://www.linkedin.com/company/thesemanticsco/", twitter: "https://twitter.com/thesemantics" }
                    },
                  ].map((member, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 50 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, ease: "easeOut" }}
                      className="group text-center"
                    >
                      <div className="relative mb-6">
                        <div className="w-32 h-32 mx-auto rounded-full overflow-hidden border-4 border-white/10 group-hover:border-blue-400/50 transition-colors duration-300">
                          <img
                            src={member.image}
                            alt={member.name}
                            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                          />
                        </div>
                      </div>
                      <h4 className="text-xl font-bold text-white mb-2">{member.name}</h4>
                      <p className="text-blue-400 font-medium mb-3">{member.role}</p>
                      <p className="text-gray-400 text-sm mb-4 leading-relaxed">{member.bio}</p>
                      <div className="flex justify-center space-x-3">
                        <a href={member.social.github} className="text-gray-400 hover:text-white transition-colors" aria-label={`${member.name}'s GitHub profile`}>
                          <Github className="w-5 h-5" />
                        </a>
                        <a href={member.social.linkedin} className="text-gray-400 hover:text-white transition-colors" aria-label={`${member.name}'s LinkedIn profile`}>
                          <Linkedin className="w-5 h-5" />
                        </a>
                        <a href={member.social.twitter} className="text-gray-400 hover:text-white transition-colors" aria-label={`${member.name}'s Twitter profile`}>
                          <Twitter className="w-5 h-5" />
                        </a>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-20 px-8">
          <div className="max-w-6xl mx-auto bg-black/30 backdrop-blur-lg rounded-3xl p-12 border border-white/10">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400 mb-4">
                What Our Clients Say
              </h2>
              <p className="text-gray-400 text-lg max-w-2xl mx-auto">
                Don't just take our word for it. See what our satisfied clients have to say about their experience working with us.
              </p>
            </motion.div>

            <div className="flex justify-center">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                <CommentSwapper comments={sampleComments} />
              </motion.div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section className="py-20 px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-black/30 backdrop-blur-lg rounded-3xl p-12 border border-white/10"
            >
              <h2 className="text-4xl font-bold text-white mb-6">
                Ready to Start Your Project?
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Let's discuss your development needs and create something extraordinary together.
              </p>

              <div className="flex flex-col sm:flex-row gap-6 items-center justify-center mb-8">
                <button
                  onClick={() => {
                    const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=${encodeURIComponent('General Inquiry - The Semantics')}&body=${encodeURIComponent('Hello The Semantics Team,\n\nI would like to get in touch regarding your services.\n\nInquiry Details:\n- Service Interest: \n- Project Type: \n- Timeline: \n- Additional Information: \n\nPlease contact me to discuss further.\n\nThank you!\n\nBest regards')}`;
                    window.open(gmailUrl, '_blank');
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold transition-colors duration-200 flex items-center gap-2 cursor-pointer"
                >
                  <MessageSquare className="w-5 h-5" />
                  Get In Touch
                </button>
                <a href="https://www.linkedin.com/company/thesemanticsco/" target="_blank" rel="noopener noreferrer" className="border border-gray-600 hover:border-gray-400 text-white px-8 py-4 rounded-lg font-semibold transition-colors duration-200 flex items-center gap-2">
                  <Globe className="w-5 h-5" />
                  LinkedIn
                </a>
              </div>

              <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-400">
                <a href="https://www.fiverr.com/tahirccreative" target="_blank" rel="noopener noreferrer" className="hover:text-white transition-colors">
                  Fiverr Profile
                </a>
                <a href="https://www.upwork.com/freelancers/~01e36322be7abf3c0a" target="_blank" rel="noopener noreferrer" className="hover:text-white transition-colors">
                  Upwork Profile
                </a>
                <span><EMAIL></span>
              </div>
            </motion.div>
          </div>
        </section>
      </div>

      {/* Modal for Tech Info */}
      <AnimatePresence>
        {selectedTech && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={closeModal}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ type: "spring", damping: 20, stiffness: 300 }}
              className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-8 max-w-md w-full border border-white/20 relative"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={closeModal}
                className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-6 h-6" />
              </button>

              <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${selectedTech.color} flex items-center justify-center mb-4`}>
                {selectedTech.icon}
              </div>

              <h2 className="text-2xl font-bold text-white mb-4">{selectedTech.title}</h2>
              <p className="text-gray-300 mb-6 leading-relaxed">{selectedTech.description}</p>

              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-white">Perfect for:</h3>
                <ul className="space-y-2">
                  {selectedTech.features.map((feature, index) => (
                    <li key={index} className="text-gray-300 flex items-center">
                      <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
