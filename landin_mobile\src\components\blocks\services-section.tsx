'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Smartphone, Monitor, Zap, Shield, Palette, Code2, Globe, Rocket } from 'lucide-react';

export function ServicesSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const services = [
    {
      icon: Smartphone,
      title: "Native iOS Development",
      description: "Swift and Objective-C powered apps with native performance and platform-specific features.",
      features: ["Swift & Objective-C", "Core Data", "Push Notifications", "App Store Optimization"],
      gradient: "from-blue-500 to-cyan-500",
      bgGradient: "from-blue-500/10 to-cyan-500/10",
    },
    {
      icon: Monitor,
      title: "Native Android Development",
      description: "Kotlin and Java applications optimized for the Android ecosystem and Google Play Store.",
      features: ["Kotlin & Java", "Room Database", "Material Design", "Google Play Services"],
      gradient: "from-green-500 to-emerald-500",
      bgGradient: "from-green-500/10 to-emerald-500/10",
    },
    {
      icon: Code2,
      title: "Cross-Platform Development",
      description: "React Native and Flutter solutions for efficient multi-platform deployment.",
      features: ["React Native", "Flutter", "Shared Codebase", "Platform Bridges"],
      gradient: "from-purple-500 to-pink-500",
      bgGradient: "from-purple-500/10 to-pink-500/10",
    },
    {
      icon: Palette,
      title: "UI/UX Design",
      description: "Beautiful, intuitive interfaces designed for optimal user experience and engagement.",
      features: ["User Research", "Wireframing", "Prototyping", "Design Systems"],
      gradient: "from-orange-500 to-red-500",
      bgGradient: "from-orange-500/10 to-red-500/10",
    },
    {
      icon: Globe,
      title: "Backend Integration",
      description: "Seamless API integration and cloud services for robust mobile applications.",
      features: ["REST APIs", "GraphQL", "Cloud Services", "Real-time Data"],
      gradient: "from-indigo-500 to-blue-500",
      bgGradient: "from-indigo-500/10 to-blue-500/10",
    },
    {
      icon: Shield,
      title: "App Security & Testing",
      description: "Comprehensive security measures and testing protocols for reliable applications.",
      features: ["Security Audits", "Automated Testing", "Performance Testing", "Code Review"],
      gradient: "from-teal-500 to-green-500",
      bgGradient: "from-teal-500/10 to-green-500/10",
    },
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-black to-gray-900">
      <div className="container mx-auto px-4">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent"
          >
            Our Services
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed"
          >
            From concept to deployment, we provide comprehensive mobile development services 
            tailored to your business needs and user expectations.
          </motion.p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {services.map((service, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className={`relative group p-8 rounded-2xl bg-gradient-to-br ${service.bgGradient} backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-300 hover:transform hover:scale-105`}
            >
              {/* Background glow effect */}
              <div className={`absolute inset-0 bg-gradient-to-br ${service.gradient} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-300`} />
              
              <div className="relative z-10">
                <div className={`inline-flex p-3 rounded-xl bg-gradient-to-br ${service.gradient} mb-6`}>
                  <service.icon className="w-6 h-6 text-white" />
                </div>
                
                <h3 className="text-2xl font-bold text-white mb-4">
                  {service.title}
                </h3>
                
                <p className="text-gray-300 mb-6 leading-relaxed">
                  {service.description}
                </p>
                
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-gray-400">
                      <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${service.gradient} mr-3`} />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mt-16"
        >
          <motion.button
            onClick={() => {
              const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=${encodeURIComponent('Mobile App Development Project - The Semantics')}&body=${encodeURIComponent('Hello The Semantics Team,\n\nI am interested in starting a mobile app development project with your team.\n\nProject Details:\n- App Type: (iOS/Android/Cross-platform)\n- Features Required: \n- Timeline: \n- Budget Range: \n- Additional Requirements: \n\nPlease contact me to discuss further.\n\nThank you!\n\nBest regards')}`;
              window.open(gmailUrl, '_blank');
            }}
            className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-4 px-8 rounded-full transition-all duration-300 flex items-center gap-2 mx-auto shadow-lg hover:shadow-xl cursor-pointer"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Rocket className="w-5 h-5" />
            Start Your Project
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
