{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_35b02a57.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_35b02a57-module__BOCH1a__className\",\n  \"variable\": \"geist_35b02a57-module__BOCH1a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_35b02a57.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_87bc4ef9.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_87bc4ef9-module__963okG__className\",\n  \"variable\": \"geist_mono_87bc4ef9-module__963okG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_87bc4ef9.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/src/components/web-vitals.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const WebVitals = registerClientReference(\n    function() { throw new Error(\"Attempted to call WebVitals() from the server but WebVitals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/web-vitals.tsx <module evaluation>\",\n    \"WebVitals\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+DACA", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/src/components/web-vitals.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const WebVitals = registerClientReference(\n    function() { throw new Error(\"Attempted to call WebVitals() from the server but WebVitals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/web-vitals.tsx\",\n    \"WebVitals\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,2CACA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from \"next/font/google\";\nimport { WebVitals } from \"@/components/web-vitals\";\nimport \"./globals.css\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nexport const metadata: Metadata = {\n  title: \"The Semantics - Web Development & Desktop Apps\",\n  description: \"Transform your business with cutting-edge web development and desktop applications. Expert full-stack development and modern UI/UX design.\",\n  keywords: [\"web development\", \"desktop applications\", \"full-stack development\", \"UI/UX design\", \"enterprise solutions\", \"modern web apps\", \"custom software\"],\n  authors: [{ name: \"The Semantics Team\" }],\n  creator: \"The Semantics\",\n  publisher: \"The Semantics\",\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      \"max-video-preview\": -1,\n      \"max-image-preview\": \"large\",\n      \"max-snippet\": -1,\n    },\n  },\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    url: \"https://desktop.thesemantics.com\",\n    title: \"The Semantics - Web Development & Desktop Apps\",\n    description: \"Transform your business with cutting-edge web development and desktop applications. Expert full-stack development and modern UI/UX design.\",\n    siteName: \"The Semantics\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"The Semantics - Web Development & Desktop Apps\",\n    description: \"Transform your business with cutting-edge web development and desktop applications. Expert full-stack development and modern UI/UX design.\",\n    creator: \"@thesemantics\",\n  },\n  verification: {\n    google: \"your-google-verification-code\",\n  },\n  alternates: {\n    canonical: \"https://desktop.thesemantics.com\",\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  const structuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Organization\",\n    \"name\": \"The Semantics\",\n    \"url\": \"https://desktop.thesemantics.com\",\n    \"logo\": \"https://thesemantics.com/logo.png\",\n    \"description\": \"Professional web development and desktop application solutions\",\n    \"foundingDate\": \"2020\",\n    \"sameAs\": [\n      \"https://linkedin.com/company/thesemantics\",\n      \"https://github.com/thesemantics\",\n      \"https://twitter.com/thesemantics\"\n    ],\n    \"contactPoint\": {\n      \"@type\": \"ContactPoint\",\n      \"telephone\": \"******-0123\",\n      \"contactType\": \"customer service\",\n      \"availableLanguage\": \"English\"\n    },\n    \"address\": {\n      \"@type\": \"PostalAddress\",\n      \"addressCountry\": \"US\"\n    },\n    \"service\": [\n      {\n        \"@type\": \"Service\",\n        \"name\": \"Web Development\",\n        \"description\": \"Custom web application development using modern technologies\"\n      },\n      {\n        \"@type\": \"Service\",\n        \"name\": \"Desktop Applications\",\n        \"description\": \"Cross-platform desktop application development\"\n      }\n    ]\n  };\n\n  return (\n    <html lang=\"en\">\n      <head>\n        {/* Explicit meta tags for better SEO */}\n        <meta name=\"description\" content=\"Transform your business with cutting-edge web development and desktop applications. Expert full-stack development and modern UI/UX design.\" />\n        <meta name=\"keywords\" content=\"web development, desktop applications, full-stack development, UI/UX design, enterprise solutions, modern web apps, custom software\" />\n        <meta name=\"author\" content=\"The Semantics Team\" />\n        <meta name=\"robots\" content=\"index, follow\" />\n\n        {/* Preconnect to external domains for better performance */}\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\n        <link rel=\"preconnect\" href=\"https://images.unsplash.com\" />\n        <link rel=\"dns-prefetch\" href=\"https://www.linkedin.com\" />\n        <link rel=\"dns-prefetch\" href=\"https://www.upwork.com\" />\n        <link rel=\"dns-prefetch\" href=\"https://www.fiverr.com\" />\n\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{\n            __html: JSON.stringify(structuredData),\n          }}\n        />\n      </head>\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased`}\n      >\n        <WebVitals />\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;;;;;;AAeO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAmB;QAAwB;QAA0B;QAAgB;QAAwB;QAAmB;KAAkB;IAC7J,SAAS;QAAC;YAAE,MAAM;QAAqB;KAAE;IACzC,SAAS;IACT,WAAW;IACX,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,OAAO;QACP,aAAa;QACb,UAAU;IACZ;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;IACX;IACA,cAAc;QACZ,QAAQ;IACV;IACA,YAAY;QACV,WAAW;IACb;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,eAAe;QACf,gBAAgB;QAChB,UAAU;YACR;YACA;YACA;SACD;QACD,gBAAgB;YACd,SAAS;YACT,aAAa;YACb,eAAe;YACf,qBAAqB;QACvB;QACA,WAAW;YACT,SAAS;YACT,kBAAkB;QACpB;QACA,WAAW;YACT;gBACE,SAAS;gBACT,QAAQ;gBACR,eAAe;YACjB;YACA;gBACE,SAAS;gBACT,QAAQ;gBACR,eAAe;YACjB;SACD;IACH;IAEA,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCAEC,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAC9B,8OAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;kCAC5B,8OAAC;wBAAK,MAAK;wBAAS,SAAQ;;;;;;kCAG5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;kCACpE,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAC9B,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAC9B,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAE9B,8OAAC;wBACC,MAAK;wBACL,yBAAyB;4BACvB,QAAQ,KAAK,SAAS,CAAC;wBACzB;;;;;;;;;;;;0BAGJ,8OAAC;gBACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;;kCAEpE,8OAAC,mIAAA,CAAA,YAAS;;;;;oBACT;;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}