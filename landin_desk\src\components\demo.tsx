"use client";

import { OptimizedScene } from "./lazy-loader";
import { GlowCard } from "@/components/ui/spotlight-card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PortfolioModal } from "./portfolio-modal";
import { motion } from "framer-motion";
import { useState } from "react";
import {
  Monitor,
  Code,
  Zap,
  Shield,
  Layers,
  Cpu,
  Database,
  Cloud,
  Star,
  Quote,
  ArrowRight,
  CheckCircle,
  Users,
  Award,
  Rocket,
  Clock,
  Target,
  Github,
  Linkedin,
  Twitter
} from "lucide-react";

// Hero Section
const HeroSection = ({ onOpenPortfolio }: { onOpenPortfolio: () => void }) => {
  return (
    <section className="min-h-screen flex items-center justify-center relative z-10 px-8" aria-label="Hero section">
      <div className="max-w-6xl mx-auto text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8"
        >
          <Badge variant="secondary" className="backdrop-blur-sm bg-white/10 border border-white/20 text-white hover:bg-white/20 px-6 py-3 rounded-full text-sm">
            ⚡ Leading Desktop Development Solutions
          </Badge>

          <h1 className="text-4xl md:text-7xl font-bold tracking-tight text-white max-w-4xl mx-auto">
            Professional Web Development & <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">Desktop Solutions</span>
          </h1>

          <p className="text-xl md:text-2xl text-neutral-300 max-w-3xl mx-auto leading-relaxed">
            Crafting powerful, intuitive desktop applications that transform your business operations and user experiences with modern web development.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 items-center justify-center pt-8">
            <Button
              onClick={() => {
                const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=${encodeURIComponent('Start New Project - The Semantics')}&body=${encodeURIComponent('Hello The Semantics Team,\n\nI am interested in starting a new project with your team. Please contact me to discuss further.\n\nProject Details:\n- Project Type: \n- Timeline: \n- Budget Range: \n- Additional Requirements: \n\nThank you!\n\nBest regards')}`;
                window.open(gmailUrl, '_blank');
              }}
              className="text-lg px-10 py-4 rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer"
            >
              Start Your Project <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
            <Button
              onClick={onOpenPortfolio}
              variant="outline"
              className="text-lg px-10 py-4 rounded-xl bg-transparent text-white border-2 border-white/30 hover:bg-white/10 transition-all duration-300 cursor-pointer"
            >
              View Portfolio
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

// Features Section
const FeaturesSection = () => {
  const features = [
    {
      icon: Monitor,
      title: "Cross-Platform Development",
      description: "Build once, deploy everywhere. Our applications run seamlessly on Windows, macOS, and Linux.",
      color: "blue" as const
    },
    {
      icon: Zap,
      title: "High Performance",
      description: "Optimized code and efficient algorithms ensure lightning-fast performance and minimal resource usage.",
      color: "purple" as const
    },
    {
      icon: Shield,
      title: "Enterprise Security",
      description: "Bank-level security protocols and encryption to protect your sensitive business data.",
      color: "green" as const
    },
    {
      icon: Layers,
      title: "Modular Architecture",
      description: "Scalable and maintainable codebase that grows with your business needs.",
      color: "orange" as const
    },
    {
      icon: Database,
      title: "Database Integration",
      description: "Seamless integration with SQL, NoSQL, and cloud databases for robust data management.",
      color: "blue" as const
    },
    {
      icon: Cloud,
      title: "Cloud Connectivity",
      description: "Built-in cloud synchronization and API integration for modern connected applications.",
      color: "purple" as const
    }
  ];

  return (
    <section className="py-32 px-8 relative z-10" aria-label="Features and benefits">
      <div className="max-w-7xl mx-auto">
        <header className="text-center mb-20">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Why Choose <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">The Semantics</span>
            </h2>
            <p className="text-xl text-neutral-300 max-w-3xl mx-auto">
              We deliver cutting-edge desktop applications with unmatched performance, security, and user experience for modern businesses.
            </p>
          </motion.div>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <GlowCard
                glowColor={feature.color}
                customSize={true}
                className="h-80 w-full"
              >
                <div className="flex flex-col h-full justify-between text-white">
                  <div className="space-y-4">
                    <feature.icon className="w-12 h-12 text-white/90" />
                    <h3 className="text-xl font-semibold">{feature.title}</h3>
                    <p className="text-neutral-300 text-sm leading-relaxed">{feature.description}</p>
                  </div>
                </div>
              </GlowCard>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Services Section
const ServicesSection = () => {
  const services = [
    {
      icon: Code,
      title: "Custom Desktop Applications",
      description: "Tailored solutions built from scratch to meet your specific business requirements.",
      features: ["Native Performance", "Custom UI/UX", "Full Source Code", "Lifetime Support"]
    },
    {
      icon: Cpu,
      title: "Legacy System Modernization",
      description: "Transform your outdated systems into modern, efficient desktop applications.",
      features: ["Data Migration", "API Integration", "Performance Optimization", "Training Included"]
    },
    {
      icon: Rocket,
      title: "Enterprise Solutions",
      description: "Scalable desktop applications designed for large organizations and complex workflows.",
      features: ["Multi-user Support", "Advanced Security", "Cloud Integration", "24/7 Support"]
    }
  ];

  return (
    <section className="py-32 px-8 relative z-10">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">Services</span>
          </h2>
          <p className="text-xl text-neutral-300 max-w-3xl mx-auto">
            Comprehensive desktop development services to power your business forward.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              viewport={{ once: true }}
            >
              <GlowCard
                glowColor={index % 2 === 0 ? "blue" : "purple"}
                customSize={true}
                className="h-96 w-full"
              >
                <div className="flex flex-col h-full justify-between text-white">
                  <div className="space-y-6">
                    <service.icon className="w-12 h-12 text-white/90" />
                    <div>
                      <h3 className="text-2xl font-semibold mb-3">{service.title}</h3>
                      <p className="text-neutral-300 text-sm leading-relaxed mb-4">{service.description}</p>
                    </div>
                    <ul className="space-y-2">
                      {service.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-sm text-neutral-300">
                          <CheckCircle className="w-4 h-4 text-green-400 mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </GlowCard>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Reviews Section
const ReviewsSection = () => {
  // Generate unique desktop-themed placeholder avatar
  const getDesktopPlaceholder = (name: string, index: number) => {
    const colors = ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B'];
    const color = colors[index % colors.length];
    const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="150" height="150" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad${index}" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${color}80;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="150" height="150" fill="url(#grad${index})" rx="20"/>
        <rect x="20" y="20" width="110" height="70" fill="white" fill-opacity="0.2" rx="8"/>
        <text x="75" y="110" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">${initials}</text>
        <circle cx="40" cy="40" r="4" fill="white" fill-opacity="0.8"/>
        <circle cx="55" cy="40" r="4" fill="white" fill-opacity="0.6"/>
        <circle cx="70" cy="40" r="4" fill="white" fill-opacity="0.4"/>
      </svg>
    `)}`;
  };

  const reviews = [
    {
      name: "Aaron Starrett",
      position: "Business Owner",
      company: "United States",
      rating: 5,
      review: "Tahir Siddique exceeded expectations with his professionalism and well-documented software solutions. Working with him was seamless due to his proactive communication and fluency, not to mention he always delivered on time. Always great to work with, will help you before during and after delivery. His software works well and he is always able to adjust things as needed. HIGHLY recommended!",
      avatar: getDesktopPlaceholder("Aaron Starrett", 0)
    },
    {
      name: "King Co",
      position: "Project Manager",
      company: "United States",
      rating: 5,
      review: "Wow, Tahir is amazing. I have tried to work with a few developers on Fiverr and none of them ever proactively went deep enough with me to truly understand the scope of the project. Tahir exceeded my expectations and will be working with him in the future!! His documentation was clear and comprehensive, reflecting his deep understanding and expertise in code. Highly recommend!",
      avatar: getDesktopPlaceholder("King Co", 1)
    },
    {
      name: "Yunik Design",
      position: "Design Director",
      company: "France",
      rating: 5,
      review: "I had the pleasure of working with Tahir, and I must say, he truly exceeded all my expectations. His documentation was clear and comprehensive, reflecting his deep understanding and expertise in code. Every detail was meticulously attended to, ensuring the final product was completely bug-free. The quality of work and attention to detail was exceptional.",
      avatar: getDesktopPlaceholder("Yunik Design", 2)
    },
    {
      name: "Thibault Armand",
      position: "Data Analyst",
      company: "United States",
      rating: 5,
      review: "I had the pleasure to hire Tahir for a data scrapping work. His understanding of the problem, ability to propose solutions and deliver them were outstanding. Very professional and knowledgeable! He understood my project perfectly and created exactly what I needed. I would highly recommend working with him and would be happy to do so again.",
      avatar: getDesktopPlaceholder("Thibault Armand", 3)
    }
  ];

  return (
    <section className="py-32 px-8 relative z-10">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Client <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">Success Stories</span>
          </h2>
          <p className="text-xl text-neutral-300 max-w-3xl mx-auto">
            Don&apos;t just take our word for it. See what our clients say about working with The Semantics.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {reviews.map((review, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <GlowCard
                glowColor={index % 3 === 0 ? "blue" : index % 3 === 1 ? "purple" : "green"}
                customSize={true}
                className="h-80 w-full"
              >
                <div className="flex flex-col h-full justify-between text-white">
                  <div className="space-y-4">
                    <Quote className="w-8 h-8 text-white/60" />
                    <p className="text-neutral-300 text-sm leading-relaxed italic">&ldquo;{review.review}&rdquo;</p>
                    <div className="flex items-center space-x-1">
                      {[...Array(review.rating)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                  </div>
                  <div className="flex items-center space-x-4 pt-4 border-t border-white/10">
                    <img
                      src={review.avatar}
                      alt={review.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <h4 className="font-semibold text-white">{review.name}</h4>
                      <p className="text-xs text-neutral-400">{review.position}</p>
                      <p className="text-xs text-neutral-500">{review.company}</p>
                    </div>
                  </div>
                </div>
              </GlowCard>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

// About Section
const AboutSection = () => {
  const stats = [
    { icon: Users, number: "500+", label: "Happy Clients" },
    { icon: Award, number: "4.9/5", label: "Client Rating" },
    { icon: Clock, number: "8+", label: "Years Experience" },
    { icon: Target, number: "1000+", label: "Projects Delivered" },
  ];

  const team = [
    {
      name: "Umar Javed",
      role: "Founder & CEO",
      image: "/umar-javed.jpg",
      bio: "Expert in modern web technologies, AI integration, and scalable application architecture. Specializes in React, Node.js, and machine learning implementations.",
      social: { github: "https://github.com/thesemantics", linkedin: "https://www.linkedin.com/company/thesemanticsco/", twitter: "https://twitter.com/thesemantics" }
    },
    {
      name: "Tahir Siddique",
      role: "Co-Founder & Full Stack Developer",
      image: "/tahir-siddique.jpg",
      bio: "Full stack developer specializing in Python, web scraping, and automation solutions. Expert in creating scalable web applications with a 4.9/5 rating on Fiverr.",
      social: { github: "https://github.com/tahirccreative", linkedin: "https://www.linkedin.com/in/tahirccreative/", twitter: "https://twitter.com/tahirccreative" }
    },
    {
      name: "James Mitchell",
      role: "UI/UX Designer & Frontend Specialist",
      image: "https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=300&h=300&fit=crop&crop=face",
      bio: "Creative designer with a passion for crafting intuitive user experiences. Combines aesthetic design with functional frontend development.",
      social: { github: "#", linkedin: "#", twitter: "#" }
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section className="py-20 bg-gradient-to-b from-gray-900 to-black relative z-10">
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent">
              About Us
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              We&apos;re a passionate team of desktop developers, designers, and strategists
              dedicated to creating exceptional desktop applications that drive business growth.
            </p>
          </motion.div>

          {/* Stats */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="text-center group"
              >
                <div className="inline-flex p-4 rounded-xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/10 mb-4 group-hover:scale-110 transition-transform duration-300">
                  <stat.icon className="w-8 h-8 text-blue-400" />
                </div>
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-400 font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Company Story */}
          <motion.div
            variants={itemVariants}
            className="grid md:grid-cols-2 gap-12 items-center mb-20"
          >
            <div>
              <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Our Story
              </h3>
              <div className="space-y-4 text-gray-300 leading-relaxed">
                <p>
                  Founded in 2016, we started as a small team of passionate developers
                  who believed that desktop technology could transform businesses and
                  improve people&apos;s productivity.
                </p>
                <p>
                  Today, we&apos;ve grown into a full-service desktop development agency,
                  helping startups and enterprises build world-class desktop applications
                  that users love and businesses depend on.
                </p>
                <p>
                  Our commitment to quality, innovation, and client success has made us
                  a trusted partner for companies looking to make their mark in the desktop world.
                </p>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-square rounded-2xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/10 p-8 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-6xl mb-4">💻</div>
                  <div className="text-2xl font-bold text-white mb-2">Innovation First</div>
                  <div className="text-gray-400">Pushing boundaries in desktop development</div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Team */}
          <motion.div variants={containerVariants}>
            <motion.div variants={itemVariants} className="text-center mb-12">
              <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Meet Our Team
              </h3>
              <p className="text-gray-300 max-w-2xl mx-auto">
                The talented individuals behind our success, each bringing unique expertise
                and passion to every project.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-3 gap-8">
              {team.map((member, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="group text-center"
                >
                  <div className="relative mb-6">
                    <div className="w-32 h-32 mx-auto rounded-full overflow-hidden border-4 border-white/10 group-hover:border-blue-400/50 transition-colors duration-300">
                      <img
                        src={member.image}
                        alt={member.name}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                    </div>
                  </div>
                  <h4 className="text-xl font-bold text-white mb-2">{member.name}</h4>
                  <p className="text-blue-400 font-medium mb-3">{member.role}</p>
                  <p className="text-gray-400 text-sm mb-4 leading-relaxed">{member.bio}</p>
                  <div className="flex justify-center space-x-3">
                    <a href={member.social.github} className="text-gray-400 hover:text-white transition-colors" aria-label={`${member.name}'s GitHub profile`}>
                      <Github className="w-5 h-5" />
                    </a>
                    <a href={member.social.linkedin} className="text-gray-400 hover:text-white transition-colors" aria-label={`${member.name}'s LinkedIn profile`}>
                      <Linkedin className="w-5 h-5" />
                    </a>
                    <a href={member.social.twitter} className="text-gray-400 hover:text-white transition-colors" aria-label={`${member.name}'s Twitter profile`}>
                      <Twitter className="w-5 h-5" />
                    </a>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};



// Contact Section
const ContactSection = ({ onOpenPortfolio }: { onOpenPortfolio: () => void }) => {
  return (
    <section className="py-32 px-8 relative z-10">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Ready to Transform Your <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">Business?</span>
          </h2>
          <p className="text-xl text-neutral-300 max-w-2xl mx-auto">
            Let&apos;s discuss your desktop application needs and create something extraordinary together.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <GlowCard glowColor="blue" customSize={true} className="h-full w-full">
              <div className="p-8 text-white space-y-8">
                <h3 className="text-2xl font-semibold mb-6">Get In Touch</h3>

                <div className="space-y-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-neutral-400">Email Us</p>
                      <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 transition-colors">
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-neutral-400">LinkedIn</p>
                      <a href="https://www.linkedin.com/company/thesemanticsco/" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300 transition-colors">
                        The Semantics Company
                      </a>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-green-600/20 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M18.561 13.158c-1.102 0-2.135-.467-3.074-1.227l.228-1.076.008-.042c.207-1.143.849-3.06 2.839-3.06 1.705 0 2.707 1.614 2.707 3.17 0 1.104-.365 2.235-1.708 2.235z"/>
                        <path d="M15.84 5.77c-.164-.093-.356-.153-.566-.153-.496 0-.934.33-1.073.805l-.017.058c-.01.026-.016.055-.016.085 0 .084.027.162.074.226.047.064.113.113.191.138l.021.007c.049.016.102.024.156.024.496 0 .934-.33 1.073-.805l.017-.058c.01-.026.016-.055.016-.085 0-.084-.027-.162-.074-.226-.047-.064-.113-.113-.191-.138l-.021-.007c-.049-.016-.102-.024-.156-.024-.2 0-.39.06-.566.153z"/>
                        <path d="M24 12c0 6.627-5.373 12-12 12S0 18.627 0 12 5.373 0 12 0s12 5.373 12 12zM9.5 7.5c0-1.381 1.119-2.5 2.5-2.5s2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5-2.5-1.119-2.5-2.5zm7 9c0 1.933-1.567 3.5-3.5 3.5s-3.5-1.567-3.5-3.5 1.567-3.5 3.5-3.5 3.5 1.567 3.5 3.5z"/>
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-neutral-400">Upwork Profile</p>
                      <a href="https://www.upwork.com/freelancers/~01e36322be7abf3c0a" target="_blank" rel="noopener noreferrer" className="text-green-400 hover:text-green-300 transition-colors">
                        View Our Upwork Profile
                      </a>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.004 15.588a.995.995 0 0 0-.804-.98l-2.626-.526a.995.995 0 0 0-1.078.54l-1.17 2.05a15.963 15.963 0 0 1-6.345-6.345l2.05-1.17a.995.995 0 0 0 .54-1.078l-.526-2.626a.995.995 0 0 0-.98-.804A10.741 10.741 0 0 0 8.5 4C3.806 4 0 7.806 0 12.5S3.806 21 8.5 21c4.632 0 8.424-3.73 8.504-8.412z"/>
                        <path d="M12.5 0C5.596 0 0 5.596 0 12.5S5.596 25 12.5 25 25 19.404 25 12.5 19.404 0 12.5 0zm6.25 18.75h-12.5v-12.5h12.5v12.5z"/>
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-neutral-400">Fiverr Profile</p>
                      <a href="https://www.fiverr.com/tahirccreative" target="_blank" rel="noopener noreferrer" className="text-purple-400 hover:text-purple-300 transition-colors">
                        View Our Fiverr Profile
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </GlowCard>
          </motion.div>

          {/* CTA */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="flex flex-col justify-center"
          >
            <div className="space-y-8">
              <div>
                <h3 className="text-3xl font-bold text-white mb-4">Start Your Project Today</h3>
                <p className="text-neutral-300 text-lg leading-relaxed">
                  Ready to bring your vision to life? Contact us for a free consultation and let&apos;s discuss how we can help transform your business with cutting-edge technology solutions.
                </p>
              </div>

              <div className="space-y-4">
                <Button
                  onClick={() => {
                    const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=${encodeURIComponent('Free Consultation Request - The Semantics')}&body=${encodeURIComponent('Hello The Semantics Team,\n\nI would like to schedule a free consultation to discuss my project requirements.\n\nProject Details:\n- Project Type: \n- Timeline: \n- Budget Range: \n- Additional Notes: \n\nPlease let me know your availability.\n\nThank you!\n\nBest regards')}`;
                    window.open(gmailUrl, '_blank');
                  }}
                  className="w-full text-lg px-12 py-4 rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer"
                >
                  Get Free Consultation <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
                <Button
                  onClick={onOpenPortfolio}
                  variant="outline"
                  className="w-full text-lg px-12 py-4 rounded-xl bg-transparent text-white border-2 border-white/30 hover:bg-white/10 transition-all duration-300 cursor-pointer"
                >
                  View Our Portfolio
                </Button>
              </div>

              <div className="pt-6 border-t border-white/10">
                <p className="text-sm text-neutral-400 text-center">
                  Trusted by 500+ clients worldwide • 15+ years of experience • 99% client satisfaction
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

// Main Component
const DemoOne = () => {
  const [isPortfolioModalOpen, setIsPortfolioModalOpen] = useState(false);

  return (
    <div className="min-h-screen w-screen bg-gradient-to-br from-[#000] to-[#1A2428] relative overflow-x-hidden">
      {/* 3D Background - Lazy loaded for better performance */}
      <div className='fixed inset-0 z-0'>
        <OptimizedScene />
      </div>

      {/* Content */}
      <div className="relative z-10">
        <HeroSection onOpenPortfolio={() => setIsPortfolioModalOpen(true)} />
        <FeaturesSection />
        <ServicesSection />
        <ReviewsSection />
        <AboutSection />
        <ContactSection onOpenPortfolio={() => setIsPortfolioModalOpen(true)} />
      </div>

      {/* Portfolio Modal */}
      <PortfolioModal
        isOpen={isPortfolioModalOpen}
        onClose={() => setIsPortfolioModalOpen(false)}
      />
    </div>
  );
};

export { DemoOne };
