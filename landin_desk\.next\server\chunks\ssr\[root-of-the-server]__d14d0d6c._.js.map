{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/src/components/demo.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DemoOne = registerClientReference(\n    function() { throw new Error(\"Attempted to call DemoOne() from the server but DemoOne is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/demo.tsx <module evaluation>\",\n    \"DemoOne\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,yDACA", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/src/components/demo.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DemoOne = registerClientReference(\n    function() { throw new Error(\"Attempted to call DemoOne() from the server but DemoOne is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/demo.tsx\",\n    \"DemoOne\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,qCACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/src/app/page.tsx"], "sourcesContent": ["import { DemoOne } from \"@/components/demo\";\nimport { Metadata } from \"next\";\n\nexport const metadata: Metadata = {\n  title: \"The Semantics - Professional Web Development & Desktop Solutions\",\n  description: \"Transform your business with cutting-edge web development and desktop applications. Expert full-stack development, modern UI/UX design, and scalable solutions for enterprises.\",\n  openGraph: {\n    title: \"The Semantics - Professional Web Development & Desktop Solutions\",\n    description: \"Transform your business with cutting-edge web development and desktop applications.\",\n    type: \"website\",\n  },\n};\n\nexport default function Home() {\n  return (\n    <main className=\"min-h-screen\" role=\"main\" aria-label=\"Main content\">\n      <DemoOne />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;IACR;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAK,WAAU;QAAe,MAAK;QAAO,cAAW;kBACpD,cAAA,8OAAC,0HAAA,CAAA,UAAO;;;;;;;;;;AAGd", "debugId": null}}]}