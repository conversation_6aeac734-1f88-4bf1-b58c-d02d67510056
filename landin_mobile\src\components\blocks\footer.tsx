'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Github, 
  Linkedin, 
  Twitter, 
  Instagram,
  Smartphone,
  ArrowUp
} from 'lucide-react';

export function Footer() {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <footer className="bg-black border-t border-white/10">
      <div className="container mx-auto px-4">
        {/* Main Footer Content */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="py-16"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Company Info */}
            <motion.div variants={itemVariants} className="lg:col-span-2">
              <div className="flex items-center mb-6">
                <div className="bg-gradient-to-r from-blue-500 to-purple-500 p-2 rounded-lg mr-3">
                  <Smartphone className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-white">The Semantics</h3>
              </div>
              <p className="text-gray-400 mb-6 leading-relaxed max-w-md">
                We're passionate about creating exceptional mobile experiences that drive 
                business growth. From native iOS and Android apps to cross-platform solutions, 
                we bring your ideas to life.
              </p>
              <div className="flex space-x-4">
                <a
                  href="https://www.linkedin.com/company/thesemanticsco/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/10 rounded-full p-3 transition-all duration-300 group"
                  aria-label="The Semantics LinkedIn Company Page"
                >
                  <Linkedin className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
                </a>
                <a
                  href="https://www.fiverr.com/tahirccreative"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="The Semantics Fiverr Profile"
                  className="bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/10 rounded-full p-3 transition-all duration-300 group"
                >
                  <svg className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.5 0C5.596 0 0 5.596 0 12.5S5.596 25 12.5 25 25 19.404 25 12.5 19.404 0 12.5 0zm6.25 18.75h-12.5v-12.5h12.5v12.5z"/>
                  </svg>
                </a>
                <a
                  href="https://www.upwork.com/freelancers/~01e36322be7abf3c0a"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/10 rounded-full p-3 transition-all duration-300 group"
                  aria-label="The Semantics Upwork Profile"
                >
                  <svg className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18.561 13.158c-1.102 0-2.135-.467-3.074-1.227l.228-1.076.008-.042c.207-1.143.849-3.06 2.839-3.06 1.705 0 2.707 1.614 2.707 3.17 0 1.104-.365 2.235-1.708 2.235z"/>
                    <path d="M15.84 5.77c-.164-.093-.356-.153-.566-.153-.496 0-.934.33-1.073.805l-.017.058c-.01.026-.016.055-.016.085 0 .084.027.162.074.226.047.064.113.113.191.138l.021.007c.049.016.102.024.156.024.496 0 .934-.33 1.073-.805l.017-.058c.01-.026.016-.055.016-.085 0-.084-.027-.162-.074-.226-.047-.064-.113-.113-.191-.138l-.021-.007c-.049-.016-.102-.024-.156-.024-.2 0-.39.06-.566.153z"/>
                  </svg>
                </a>
                <button
                  onClick={() => {
                    const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=${encodeURIComponent('Mobile App Development Inquiry - The Semantics')}&body=${encodeURIComponent('Hello The Semantics Team,\n\nI am interested in your mobile app development services.\n\nProject Details:\n- App Type: \n- Requirements: \n- Timeline: \n- Budget Range: \n\nPlease contact me to discuss further.\n\nThank you!\n\nBest regards')}`;
                    window.open(gmailUrl, '_blank');
                  }}
                  className="bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/10 rounded-full p-3 transition-all duration-300 group cursor-pointer"
                  aria-label="Email The Semantics"
                >
                  <Mail className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
                </button>
              </div>
            </motion.div>

            {/* Services */}
            <motion.div variants={itemVariants}>
              <h4 className="text-lg font-semibold text-white mb-6">Services</h4>
              <ul className="space-y-3">
                {[
                  'Full Stack Development',
                  'Web Scraping & Automation',
                  'Mobile App Development',
                  'Django & Flask',
                  'UI/UX Design',
                  'Custom Web Applications'
                ].map((service, index) => (
                  <li key={index}>
                    <a
                      href="#"
                      className="text-gray-400 hover:text-white transition-colors duration-300 flex items-center group"
                    >
                      <div className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:scale-150 transition-transform" />
                      {service}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Contact Info */}
            <motion.div variants={itemVariants}>
              <h4 className="text-lg font-semibold text-white mb-6">Contact</h4>
              <div className="space-y-4">
                <div className="flex items-center text-gray-400">
                  <Mail className="w-5 h-5 mr-3 text-blue-400" />
                  <button
                    onClick={() => {
                      const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=${encodeURIComponent('Mobile App Development Inquiry - The Semantics')}&body=${encodeURIComponent('Hello The Semantics Team,\n\nI am interested in your mobile app development services.\n\nProject Details:\n- App Type: \n- Requirements: \n- Timeline: \n- Budget Range: \n\nPlease contact me to discuss further.\n\nThank you!\n\nBest regards')}`;
                      window.open(gmailUrl, '_blank');
                    }}
                    className="hover:text-white transition-colors duration-300 cursor-pointer text-left"
                  >
                    <EMAIL>
                  </button>
                </div>
                <div className="flex items-center text-gray-400">
                  <Linkedin className="w-5 h-5 mr-3 text-blue-400" />
                  <a
                    href="https://www.linkedin.com/company/thesemanticsco/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:text-white transition-colors duration-300"
                  >
                    LinkedIn Company Page
                  </a>
                </div>
                <div className="flex items-start text-gray-400">
                  <MapPin className="w-5 h-5 mr-3 text-blue-400 mt-0.5" />
                  <div>
                    <div>Remote Team</div>
                    <div>Global Services</div>
                    <div>Available Worldwide</div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>



        {/* Bottom Bar */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="py-6 border-t border-white/10 flex flex-col md:flex-row justify-between items-center"
        >
          <div className="text-gray-400 text-sm mb-4 md:mb-0">
            © 2024 The Semantics. All rights reserved.
          </div>
          <div className="flex items-center space-x-6">
            <a
              href="#"
              className="text-gray-400 hover:text-white text-sm transition-colors duration-300"
            >
              Privacy Policy
            </a>
            <a
              href="#"
              className="text-gray-400 hover:text-white text-sm transition-colors duration-300"
            >
              Terms of Service
            </a>
            <motion.button
              onClick={scrollToTop}
              className="bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/10 rounded-full p-2 transition-all duration-300 group"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ArrowUp className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" />
            </motion.button>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}
