"use client";

import { motion, AnimatePresence } from "framer-motion";
import { X, ExternalLink, Github, Linkedin, Globe, Briefcase } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface PortfolioModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const portfolioLinks = [
  { 
    name: 'Desktop Development', 
    url: 'https://desktop.thesemantics.com',
    description: 'Professional desktop applications and software solutions',
    icon: Globe,
    color: 'from-blue-500 to-blue-700'
  },
  { 
    name: 'Mobile Apps', 
    url: 'https://mobile.thesemantics.com',
    description: 'Cross-platform mobile application development',
    icon: Globe,
    color: 'from-purple-500 to-purple-700'
  },
  { 
    name: 'AI Platform', 
    url: 'https://ai.thesemantics.com',
    description: 'Artificial intelligence and machine learning solutions',
    icon: Globe,
    color: 'from-green-500 to-green-700'
  },
  { 
    name: 'Web Scraping', 
    url: 'https://scraping.thesemantics.com',
    description: 'Data extraction and web scraping services',
    icon: Globe,
    color: 'from-orange-500 to-orange-700'
  },
  { 
    name: 'Main Portfolio', 
    url: 'https://thesemantics.com',
    description: 'Complete portfolio and company information',
    icon: Briefcase,
    color: 'from-indigo-500 to-indigo-700'
  },
  { 
    name: 'GitHub Profile', 
    url: 'https://github.com/thesemantics',
    description: 'Open source projects and code repositories',
    icon: Github,
    color: 'from-gray-600 to-gray-800'
  },
  { 
    name: 'LinkedIn Company', 
    url: 'https://www.linkedin.com/company/thesemanticsco/',
    description: 'Professional network and company updates',
    icon: Linkedin,
    color: 'from-blue-600 to-blue-800'
  },
  { 
    name: 'Upwork Profile', 
    url: 'https://www.upwork.com/freelancers/~01e36322be7abf3c0a',
    description: 'Freelance projects and client testimonials',
    icon: ExternalLink,
    color: 'from-green-600 to-green-800'
  },
  { 
    name: 'Fiverr Profile', 
    url: 'https://www.fiverr.com/tahirccreative',
    description: 'Creative services and quick project solutions',
    icon: ExternalLink,
    color: 'from-purple-600 to-purple-800'
  }
];

export function PortfolioModal({ isOpen, onClose }: PortfolioModalProps) {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-gradient-to-br from-gray-900 to-black border border-white/10 rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <div>
                <h2 className="text-3xl font-bold text-white mb-2">
                  🚀 The Semantics Portfolio
                </h2>
                <p className="text-gray-400">
                  Explore our projects, services, and professional profiles
                </p>
              </div>
              <Button
                onClick={onClose}
                variant="outline"
                size="sm"
                className="border-white/20 text-white hover:bg-white/10"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {portfolioLinks.map((link, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="group"
                  >
                    <div 
                      className={`bg-gradient-to-br ${link.color} p-6 rounded-xl cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-2xl border border-white/10`}
                      onClick={() => window.open(link.url, '_blank')}
                    >
                      <div className="flex items-start justify-between mb-4">
                        <link.icon className="w-8 h-8 text-white" />
                        <ExternalLink className="w-5 h-5 text-white/60 group-hover:text-white transition-colors" />
                      </div>
                      
                      <h3 className="text-xl font-bold text-white mb-2 group-hover:text-white transition-colors">
                        {link.name}
                      </h3>
                      
                      <p className="text-white/80 text-sm leading-relaxed mb-4">
                        {link.description}
                      </p>
                      
                      <div className="text-xs text-white/60 font-mono break-all">
                        {link.url}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
