{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\n\nexport function middleware(request: NextRequest) {\n  const response = NextResponse.next()\n\n  // Add performance and security headers\n  response.headers.set('X-DNS-Prefetch-Control', 'on')\n  response.headers.set('X-Frame-Options', 'DENY')\n  response.headers.set('X-Content-Type-Options', 'nosniff')\n  response.headers.set('Referrer-Policy', 'origin-when-cross-origin')\n  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')\n  \n  // Cache static assets aggressively\n  if (request.nextUrl.pathname.startsWith('/_next/static/')) {\n    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')\n  }\n  \n  // Cache images\n  if (request.nextUrl.pathname.match(/\\.(jpg|jpeg|png|gif|webp|avif|svg)$/)) {\n    response.headers.set('Cache-Control', 'public, max-age=86400')\n  }\n\n  return response\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,SAAS,WAAW,OAAoB;IAC7C,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;IAElC,uCAAuC;IACvC,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,sBAAsB;IAE3C,mCAAmC;IACnC,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,mBAAmB;QACzD,SAAS,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACxC;IAEA,eAAe;IACf,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,wCAAwC;QACzE,SAAS,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACxC;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}