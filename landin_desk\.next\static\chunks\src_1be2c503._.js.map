{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/src/components/lazy-loader.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense, lazy } from 'react';\n\n// Lazy load heavy components\nexport const LazyScene = lazy(() => import('./ui/hero-section').then(module => ({ default: module.Scene })));\n\n// Loading fallback component\nexport function SceneLoader() {\n  return (\n    <div className=\"fixed inset-0 z-0 bg-gradient-to-br from-[#000] to-[#1A2428] flex items-center justify-center\">\n      <div className=\"text-white/50 text-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-white/30 mx-auto mb-4\"></div>\n        <p className=\"text-sm\">Loading 3D Experience...</p>\n      </div>\n    </div>\n  );\n}\n\n// Wrapper component with Suspense\nexport function OptimizedScene() {\n  return (\n    <Suspense fallback={<SceneLoader />}>\n      <LazyScene />\n    </Suspense>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAKO,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,IAAM,yIAA4B,IAAI,CAAC,CAAA,SAAU,CAAC;YAAE,SAAS,OAAO,KAAK;QAAC,CAAC;KAA5F;AAGN,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAU;;;;;;;;;;;;;;;;;AAI/B;MATgB;AAYT,SAAS;IACd,qBACE,6LAAC,6JAAA,CAAA,WAAQ;QAAC,wBAAU,6LAAC;;;;;kBACnB,cAAA,6LAAC;;;;;;;;;;AAGP;MANgB", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/src/components/ui/spotlight-card.tsx"], "sourcesContent": ["import React, { useEffect, useRef, ReactNode } from 'react';\n\ninterface GlowCardProps {\n  children: ReactNode;\n  className?: string;\n  glowColor?: 'blue' | 'purple' | 'green' | 'red' | 'orange';\n  size?: 'sm' | 'md' | 'lg';\n  width?: string | number;\n  height?: string | number;\n  customSize?: boolean; // When true, ignores size prop and uses width/height or className\n}\n\nconst glowColorMap = {\n  blue: { base: 220, spread: 200 },\n  purple: { base: 280, spread: 300 },\n  green: { base: 120, spread: 200 },\n  red: { base: 0, spread: 200 },\n  orange: { base: 30, spread: 200 }\n};\n\nconst sizeMap = {\n  sm: 'w-48 h-64',\n  md: 'w-64 h-80',\n  lg: 'w-80 h-96'\n};\n\nconst GlowCard: React.FC<GlowCardProps> = ({ \n  children, \n  className = '', \n  glowColor = 'blue',\n  size = 'md',\n  width,\n  height,\n  customSize = false\n}) => {\n  const cardRef = useRef<HTMLDivElement>(null);\n  const innerRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const syncPointer = (e: PointerEvent) => {\n      const { clientX: x, clientY: y } = e;\n      \n      if (cardRef.current) {\n        cardRef.current.style.setProperty('--x', x.toFixed(2));\n        cardRef.current.style.setProperty('--xp', (x / window.innerWidth).toFixed(2));\n        cardRef.current.style.setProperty('--y', y.toFixed(2));\n        cardRef.current.style.setProperty('--yp', (y / window.innerHeight).toFixed(2));\n      }\n    };\n\n    document.addEventListener('pointermove', syncPointer);\n    return () => document.removeEventListener('pointermove', syncPointer);\n  }, []);\n\n  const { base, spread } = glowColorMap[glowColor];\n\n  // Determine sizing\n  const getSizeClasses = () => {\n    if (customSize) {\n      return ''; // Let className or inline styles handle sizing\n    }\n    return sizeMap[size];\n  };\n\n  const getInlineStyles = () => {\n    const baseStyles = {\n      '--base': base,\n      '--spread': spread,\n      '--radius': '14',\n      '--border': '3',\n      '--backdrop': 'hsl(0 0% 60% / 0.12)',\n      '--backup-border': 'var(--backdrop)',\n      '--size': '200',\n      '--outer': '1',\n      '--border-size': 'calc(var(--border, 2) * 1px)',\n      '--spotlight-size': 'calc(var(--size, 150) * 1px)',\n      '--hue': 'calc(var(--base) + (var(--xp, 0) * var(--spread, 0)))',\n      backgroundImage: `radial-gradient(\n        var(--spotlight-size) var(--spotlight-size) at\n        calc(var(--x, 0) * 1px)\n        calc(var(--y, 0) * 1px),\n        hsl(var(--hue, 210) calc(var(--saturation, 100) * 1%) calc(var(--lightness, 70) * 1%) / var(--bg-spot-opacity, 0.1)), transparent\n      )`,\n      backgroundColor: 'var(--backdrop, transparent)',\n      backgroundSize: 'calc(100% + (2 * var(--border-size))) calc(100% + (2 * var(--border-size)))',\n      backgroundPosition: '50% 50%',\n      backgroundAttachment: 'fixed',\n      border: 'var(--border-size) solid var(--backup-border)',\n      position: 'relative' as const,\n      touchAction: 'none' as const,\n    };\n\n    // Add width and height if provided\n    if (width !== undefined) {\n      baseStyles.width = typeof width === 'number' ? `${width}px` : width;\n    }\n    if (height !== undefined) {\n      baseStyles.height = typeof height === 'number' ? `${height}px` : height;\n    }\n\n    return baseStyles;\n  };\n\n  const beforeAfterStyles = `\n    [data-glow]::before,\n    [data-glow]::after {\n      pointer-events: none;\n      content: \"\";\n      position: absolute;\n      inset: calc(var(--border-size) * -1);\n      border: var(--border-size) solid transparent;\n      border-radius: calc(var(--radius) * 1px);\n      background-attachment: fixed;\n      background-size: calc(100% + (2 * var(--border-size))) calc(100% + (2 * var(--border-size)));\n      background-repeat: no-repeat;\n      background-position: 50% 50%;\n      mask: linear-gradient(transparent, transparent), linear-gradient(white, white);\n      mask-clip: padding-box, border-box;\n      mask-composite: intersect;\n    }\n\n    [data-glow]::before {\n      background-image: radial-gradient(\n        calc(var(--spotlight-size) * 0.75) calc(var(--spotlight-size) * 0.75) at\n        calc(var(--x, 0) * 1px)\n        calc(var(--y, 0) * 1px),\n        hsl(var(--hue, 210) calc(var(--saturation, 100) * 1%) calc(var(--lightness, 50) * 1%) / var(--border-spot-opacity, 1)), transparent 100%\n      );\n      filter: brightness(2);\n    }\n\n    [data-glow]::after {\n      background-image: radial-gradient(\n        calc(var(--spotlight-size) * 0.5) calc(var(--spotlight-size) * 0.5) at\n        calc(var(--x, 0) * 1px)\n        calc(var(--y, 0) * 1px),\n        hsl(0 100% 100% / var(--border-light-opacity, 1)), transparent 100%\n      );\n    }\n\n    [data-glow] [data-glow] {\n      position: absolute;\n      inset: 0;\n      will-change: filter;\n      opacity: var(--outer, 1);\n      border-radius: calc(var(--radius) * 1px);\n      border-width: calc(var(--border-size) * 20);\n      filter: blur(calc(var(--border-size) * 10));\n      background: none;\n      pointer-events: none;\n      border: none;\n    }\n\n    [data-glow] > [data-glow]::before {\n      inset: -10px;\n      border-width: 10px;\n    }\n  `;\n\n  return (\n    <>\n      <style dangerouslySetInnerHTML={{ __html: beforeAfterStyles }} />\n      <div\n        ref={cardRef}\n        data-glow\n        style={getInlineStyles()}\n        className={`\n          ${getSizeClasses()}\n          ${!customSize ? 'aspect-[3/4]' : ''}\n          rounded-2xl\n          relative\n          grid\n          grid-rows-[1fr_auto]\n          shadow-[0_1rem_2rem_-1rem_black]\n          p-4\n          gap-4\n          backdrop-blur-[5px]\n          ${className}\n        `}\n      >\n        <div ref={innerRef} data-glow></div>\n        {children}\n      </div>\n    </>\n  );\n};\n\nexport { GlowCard };\n"], "names": [], "mappings": ";;;;AAAA;;;;AAYA,MAAM,eAAe;IACnB,MAAM;QAAE,MAAM;QAAK,QAAQ;IAAI;IAC/B,QAAQ;QAAE,MAAM;QAAK,QAAQ;IAAI;IACjC,OAAO;QAAE,MAAM;QAAK,QAAQ;IAAI;IAChC,KAAK;QAAE,MAAM;QAAG,QAAQ;IAAI;IAC5B,QAAQ;QAAE,MAAM;QAAI,QAAQ;IAAI;AAClC;AAEA,MAAM,UAAU;IACd,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEA,MAAM,WAAoC;QAAC,EACzC,QAAQ,EACR,YAAY,EAAE,EACd,YAAY,MAAM,EAClB,OAAO,IAAI,EACX,KAAK,EACL,MAAM,EACN,aAAa,KAAK,EACnB;;IACC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACvC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAExC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM;kDAAc,CAAC;oBACnB,MAAM,EAAE,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,GAAG;oBAEnC,IAAI,QAAQ,OAAO,EAAE;wBACnB,QAAQ,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC;wBACnD,QAAQ,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,OAAO,UAAU,EAAE,OAAO,CAAC;wBAC1E,QAAQ,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC;wBACnD,QAAQ,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,OAAO,WAAW,EAAE,OAAO,CAAC;oBAC7E;gBACF;;YAEA,SAAS,gBAAgB,CAAC,eAAe;YACzC;sCAAO,IAAM,SAAS,mBAAmB,CAAC,eAAe;;QAC3D;6BAAG,EAAE;IAEL,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,UAAU;IAEhD,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,IAAI,YAAY;YACd,OAAO,IAAI,+CAA+C;QAC5D;QACA,OAAO,OAAO,CAAC,KAAK;IACtB;IAEA,MAAM,kBAAkB;QACtB,MAAM,aAAa;YACjB,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,cAAc;YACd,mBAAmB;YACnB,UAAU;YACV,WAAW;YACX,iBAAiB;YACjB,oBAAoB;YACpB,SAAS;YACT,iBAAkB;YAMlB,iBAAiB;YACjB,gBAAgB;YAChB,oBAAoB;YACpB,sBAAsB;YACtB,QAAQ;YACR,UAAU;YACV,aAAa;QACf;QAEA,mCAAmC;QACnC,IAAI,UAAU,WAAW;YACvB,WAAW,KAAK,GAAG,OAAO,UAAU,WAAW,AAAC,GAAQ,OAAN,OAAM,QAAM;QAChE;QACA,IAAI,WAAW,WAAW;YACxB,WAAW,MAAM,GAAG,OAAO,WAAW,WAAW,AAAC,GAAS,OAAP,QAAO,QAAM;QACnE;QAEA,OAAO;IACT;IAEA,MAAM,oBAAqB;IAwD3B,qBACE;;0BACE,6LAAC;gBAAM,yBAAyB;oBAAE,QAAQ;gBAAkB;;;;;;0BAC5D,6LAAC;gBACC,KAAK;gBACL,WAAS;gBACT,OAAO;gBACP,WAAW,AAAC,eAER,OADA,kBAAiB,gBAUjB,OATA,CAAC,aAAa,iBAAiB,IAAG,sNASxB,OAAV,WAAU;;kCAGd,6LAAC;wBAAI,KAAK;wBAAU,WAAS;;;;;;oBAC5B;;;;;;;;;AAIT;GA/JM;KAAA", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  },\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/landing%20pages/landin_desk/src/components/demo.tsx"], "sourcesContent": ["\"use client\";\n\nimport { OptimizedScene } from \"./lazy-loader\";\nimport { GlowCard } from \"@/components/ui/spotlight-card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { motion } from \"framer-motion\";\nimport {\n  Monitor,\n  Code,\n  Zap,\n  Shield,\n  Layers,\n  Cpu,\n  Database,\n  Cloud,\n  Star,\n  Quote,\n  ArrowRight,\n  CheckCircle,\n  Users,\n  Award,\n  Rocket,\n  Clock,\n  Target,\n  Github,\n  Linkedin,\n  Twitter\n} from \"lucide-react\";\n\n// Hero Section\nconst HeroSection = () => {\n  return (\n    <section className=\"min-h-screen flex items-center justify-center relative z-10 px-8\" aria-label=\"Hero section\">\n      <div className=\"max-w-6xl mx-auto text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"space-y-8\"\n        >\n          <Badge variant=\"secondary\" className=\"backdrop-blur-sm bg-white/10 border border-white/20 text-white hover:bg-white/20 px-6 py-3 rounded-full text-sm\">\n            ⚡ Leading Desktop Development Solutions\n          </Badge>\n\n          <h1 className=\"text-4xl md:text-7xl font-bold tracking-tight text-white max-w-4xl mx-auto\">\n            Professional Web Development & <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600\">Desktop Solutions</span>\n          </h1>\n\n          <p className=\"text-xl md:text-2xl text-neutral-300 max-w-3xl mx-auto leading-relaxed\">\n            Crafting powerful, intuitive desktop applications that transform your business operations and user experiences with modern web development.\n          </p>\n\n          <div className=\"flex flex-col sm:flex-row gap-6 items-center justify-center pt-8\">\n            <Button className=\"text-lg px-10 py-4 rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n              Start Your Project <ArrowRight className=\"ml-2 w-5 h-5\" />\n            </Button>\n            <Button variant=\"outline\" className=\"text-lg px-10 py-4 rounded-xl bg-transparent text-white border-2 border-white/30 hover:bg-white/10 transition-all duration-300\">\n              View Portfolio\n            </Button>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n// Features Section\nconst FeaturesSection = () => {\n  const features = [\n    {\n      icon: Monitor,\n      title: \"Cross-Platform Development\",\n      description: \"Build once, deploy everywhere. Our applications run seamlessly on Windows, macOS, and Linux.\",\n      color: \"blue\" as const\n    },\n    {\n      icon: Zap,\n      title: \"High Performance\",\n      description: \"Optimized code and efficient algorithms ensure lightning-fast performance and minimal resource usage.\",\n      color: \"purple\" as const\n    },\n    {\n      icon: Shield,\n      title: \"Enterprise Security\",\n      description: \"Bank-level security protocols and encryption to protect your sensitive business data.\",\n      color: \"green\" as const\n    },\n    {\n      icon: Layers,\n      title: \"Modular Architecture\",\n      description: \"Scalable and maintainable codebase that grows with your business needs.\",\n      color: \"orange\" as const\n    },\n    {\n      icon: Database,\n      title: \"Database Integration\",\n      description: \"Seamless integration with SQL, NoSQL, and cloud databases for robust data management.\",\n      color: \"blue\" as const\n    },\n    {\n      icon: Cloud,\n      title: \"Cloud Connectivity\",\n      description: \"Built-in cloud synchronization and API integration for modern connected applications.\",\n      color: \"purple\" as const\n    }\n  ];\n\n  return (\n    <section className=\"py-32 px-8 relative z-10\" aria-label=\"Features and benefits\">\n      <div className=\"max-w-7xl mx-auto\">\n        <header className=\"text-center mb-20\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-4xl md:text-6xl font-bold text-white mb-6\">\n              Why Choose <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600\">The Semantics</span>\n            </h2>\n            <p className=\"text-xl text-neutral-300 max-w-3xl mx-auto\">\n              We deliver cutting-edge desktop applications with unmatched performance, security, and user experience for modern businesses.\n            </p>\n          </motion.div>\n        </header>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {features.map((feature, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <GlowCard\n                glowColor={feature.color}\n                customSize={true}\n                className=\"h-80 w-full\"\n              >\n                <div className=\"flex flex-col h-full justify-between text-white\">\n                  <div className=\"space-y-4\">\n                    <feature.icon className=\"w-12 h-12 text-white/90\" />\n                    <h3 className=\"text-xl font-semibold\">{feature.title}</h3>\n                    <p className=\"text-neutral-300 text-sm leading-relaxed\">{feature.description}</p>\n                  </div>\n                </div>\n              </GlowCard>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// Services Section\nconst ServicesSection = () => {\n  const services = [\n    {\n      icon: Code,\n      title: \"Custom Desktop Applications\",\n      description: \"Tailored solutions built from scratch to meet your specific business requirements.\",\n      features: [\"Native Performance\", \"Custom UI/UX\", \"Full Source Code\", \"Lifetime Support\"]\n    },\n    {\n      icon: Cpu,\n      title: \"Legacy System Modernization\",\n      description: \"Transform your outdated systems into modern, efficient desktop applications.\",\n      features: [\"Data Migration\", \"API Integration\", \"Performance Optimization\", \"Training Included\"]\n    },\n    {\n      icon: Rocket,\n      title: \"Enterprise Solutions\",\n      description: \"Scalable desktop applications designed for large organizations and complex workflows.\",\n      features: [\"Multi-user Support\", \"Advanced Security\", \"Cloud Integration\", \"24/7 Support\"]\n    }\n  ];\n\n  return (\n    <section className=\"py-32 px-8 relative z-10\">\n      <div className=\"max-w-7xl mx-auto\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-20\"\n        >\n          <h2 className=\"text-4xl md:text-6xl font-bold text-white mb-6\">\n            Our <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600\">Services</span>\n          </h2>\n          <p className=\"text-xl text-neutral-300 max-w-3xl mx-auto\">\n            Comprehensive desktop development services to power your business forward.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {services.map((service, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.2 }}\n              viewport={{ once: true }}\n            >\n              <GlowCard\n                glowColor={index % 2 === 0 ? \"blue\" : \"purple\"}\n                customSize={true}\n                className=\"h-96 w-full\"\n              >\n                <div className=\"flex flex-col h-full justify-between text-white\">\n                  <div className=\"space-y-6\">\n                    <service.icon className=\"w-12 h-12 text-white/90\" />\n                    <div>\n                      <h3 className=\"text-2xl font-semibold mb-3\">{service.title}</h3>\n                      <p className=\"text-neutral-300 text-sm leading-relaxed mb-4\">{service.description}</p>\n                    </div>\n                    <ul className=\"space-y-2\">\n                      {service.features.map((feature, idx) => (\n                        <li key={idx} className=\"flex items-center text-sm text-neutral-300\">\n                          <CheckCircle className=\"w-4 h-4 text-green-400 mr-2 flex-shrink-0\" />\n                          {feature}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              </GlowCard>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// Reviews Section\nconst ReviewsSection = () => {\n  // Generate unique desktop-themed placeholder avatar\n  const getDesktopPlaceholder = (name: string, index: number) => {\n    const colors = ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B'];\n    const color = colors[index % colors.length];\n    const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();\n    return `data:image/svg+xml;base64,${btoa(`\n      <svg width=\"150\" height=\"150\" xmlns=\"http://www.w3.org/2000/svg\">\n        <defs>\n          <linearGradient id=\"grad${index}\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" style=\"stop-color:${color};stop-opacity:1\" />\n            <stop offset=\"100%\" style=\"stop-color:${color}80;stop-opacity:1\" />\n          </linearGradient>\n        </defs>\n        <rect width=\"150\" height=\"150\" fill=\"url(#grad${index})\" rx=\"20\"/>\n        <rect x=\"20\" y=\"20\" width=\"110\" height=\"70\" fill=\"white\" fill-opacity=\"0.2\" rx=\"8\"/>\n        <text x=\"75\" y=\"110\" font-family=\"Arial, sans-serif\" font-size=\"24\" font-weight=\"bold\" text-anchor=\"middle\" fill=\"white\">${initials}</text>\n        <circle cx=\"40\" cy=\"40\" r=\"4\" fill=\"white\" fill-opacity=\"0.8\"/>\n        <circle cx=\"55\" cy=\"40\" r=\"4\" fill=\"white\" fill-opacity=\"0.6\"/>\n        <circle cx=\"70\" cy=\"40\" r=\"4\" fill=\"white\" fill-opacity=\"0.4\"/>\n      </svg>\n    `)}`;\n  };\n\n  const reviews = [\n    {\n      name: \"Aaron Starrett\",\n      position: \"Business Owner\",\n      company: \"United States\",\n      rating: 5,\n      review: \"Tahir Siddique exceeded expectations with his professionalism and well-documented software solutions. Working with him was seamless due to his proactive communication and fluency, not to mention he always delivered on time. Always great to work with, will help you before during and after delivery. His software works well and he is always able to adjust things as needed. HIGHLY recommended!\",\n      avatar: getDesktopPlaceholder(\"Aaron Starrett\", 0)\n    },\n    {\n      name: \"King Co\",\n      position: \"Project Manager\",\n      company: \"United States\",\n      rating: 5,\n      review: \"Wow, Tahir is amazing. I have tried to work with a few developers on Fiverr and none of them ever proactively went deep enough with me to truly understand the scope of the project. Tahir exceeded my expectations and will be working with him in the future!! His documentation was clear and comprehensive, reflecting his deep understanding and expertise in code. Highly recommend!\",\n      avatar: getDesktopPlaceholder(\"King Co\", 1)\n    },\n    {\n      name: \"Yunik Design\",\n      position: \"Design Director\",\n      company: \"France\",\n      rating: 5,\n      review: \"I had the pleasure of working with Tahir, and I must say, he truly exceeded all my expectations. His documentation was clear and comprehensive, reflecting his deep understanding and expertise in code. Every detail was meticulously attended to, ensuring the final product was completely bug-free. The quality of work and attention to detail was exceptional.\",\n      avatar: getDesktopPlaceholder(\"Yunik Design\", 2)\n    },\n    {\n      name: \"Thibault Armand\",\n      position: \"Data Analyst\",\n      company: \"United States\",\n      rating: 5,\n      review: \"I had the pleasure to hire Tahir for a data scrapping work. His understanding of the problem, ability to propose solutions and deliver them were outstanding. Very professional and knowledgeable! He understood my project perfectly and created exactly what I needed. I would highly recommend working with him and would be happy to do so again.\",\n      avatar: getDesktopPlaceholder(\"Thibault Armand\", 3)\n    }\n  ];\n\n  return (\n    <section className=\"py-32 px-8 relative z-10\">\n      <div className=\"max-w-7xl mx-auto\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-20\"\n        >\n          <h2 className=\"text-4xl md:text-6xl font-bold text-white mb-6\">\n            Client <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600\">Success Stories</span>\n          </h2>\n          <p className=\"text-xl text-neutral-300 max-w-3xl mx-auto\">\n            Don&apos;t just take our word for it. See what our clients say about working with The Semantics.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n          {reviews.map((review, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <GlowCard\n                glowColor={index % 3 === 0 ? \"blue\" : index % 3 === 1 ? \"purple\" : \"green\"}\n                customSize={true}\n                className=\"h-80 w-full\"\n              >\n                <div className=\"flex flex-col h-full justify-between text-white\">\n                  <div className=\"space-y-4\">\n                    <Quote className=\"w-8 h-8 text-white/60\" />\n                    <p className=\"text-neutral-300 text-sm leading-relaxed italic\">&ldquo;{review.review}&rdquo;</p>\n                    <div className=\"flex items-center space-x-1\">\n                      {[...Array(review.rating)].map((_, i) => (\n                        <Star key={i} className=\"w-4 h-4 fill-yellow-400 text-yellow-400\" />\n                      ))}\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-4 pt-4 border-t border-white/10\">\n                    <img\n                      src={review.avatar}\n                      alt={review.name}\n                      className=\"w-12 h-12 rounded-full object-cover\"\n                    />\n                    <div>\n                      <h4 className=\"font-semibold text-white\">{review.name}</h4>\n                      <p className=\"text-xs text-neutral-400\">{review.position}</p>\n                      <p className=\"text-xs text-neutral-500\">{review.company}</p>\n                    </div>\n                  </div>\n                </div>\n              </GlowCard>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// About Section\nconst AboutSection = () => {\n  const stats = [\n    { icon: Users, number: \"500+\", label: \"Happy Clients\" },\n    { icon: Award, number: \"4.9/5\", label: \"Client Rating\" },\n    { icon: Clock, number: \"8+\", label: \"Years Experience\" },\n    { icon: Target, number: \"1000+\", label: \"Projects Delivered\" },\n  ];\n\n  const team = [\n    {\n      name: \"Umar Javed\",\n      role: \"Founder & CEO\",\n      image: \"/umar-javed.jpg\",\n      bio: \"Expert in modern web technologies, AI integration, and scalable application architecture. Specializes in React, Node.js, and machine learning implementations.\",\n      social: { github: \"https://github.com/thesemantics\", linkedin: \"https://www.linkedin.com/company/thesemanticsco/\", twitter: \"https://twitter.com/thesemantics\" }\n    },\n    {\n      name: \"Tahir Siddique\",\n      role: \"Co-Founder & Full Stack Developer\",\n      image: \"/tahir-siddique.jpg\",\n      bio: \"Full stack developer specializing in Python, web scraping, and automation solutions. Expert in creating scalable web applications with a 4.9/5 rating on Fiverr.\",\n      social: { github: \"https://github.com/tahirccreative\", linkedin: \"https://www.linkedin.com/in/tahirccreative/\", twitter: \"https://twitter.com/tahirccreative\" }\n    },\n    {\n      name: \"James Mitchell\",\n      role: \"UI/UX Designer & Frontend Specialist\",\n      image: \"https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=300&h=300&fit=crop&crop=face\",\n      bio: \"Creative designer with a passion for crafting intuitive user experiences. Combines aesthetic design with functional frontend development.\",\n      social: { github: \"#\", linkedin: \"#\", twitter: \"#\" }\n    },\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n        duration: 0.6,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\",\n      },\n    },\n  };\n\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-gray-900 to-black relative z-10\">\n      <div className=\"container mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n        >\n          {/* Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent\">\n              About Us\n            </h2>\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n              We&apos;re a passionate team of desktop developers, designers, and strategists\n              dedicated to creating exceptional desktop applications that drive business growth.\n            </p>\n          </motion.div>\n\n          {/* Stats */}\n          <motion.div\n            variants={containerVariants}\n            className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mb-20\"\n          >\n            {stats.map((stat, index) => (\n              <motion.div\n                key={index}\n                variants={itemVariants}\n                className=\"text-center group\"\n              >\n                <div className=\"inline-flex p-4 rounded-xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/10 mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <stat.icon className=\"w-8 h-8 text-blue-400\" />\n                </div>\n                <div className=\"text-3xl md:text-4xl font-bold text-white mb-2\">\n                  {stat.number}\n                </div>\n                <div className=\"text-gray-400 font-medium\">\n                  {stat.label}\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n\n          {/* Company Story */}\n          <motion.div\n            variants={itemVariants}\n            className=\"grid md:grid-cols-2 gap-12 items-center mb-20\"\n          >\n            <div>\n              <h3 className=\"text-3xl md:text-4xl font-bold text-white mb-6\">\n                Our Story\n              </h3>\n              <div className=\"space-y-4 text-gray-300 leading-relaxed\">\n                <p>\n                  Founded in 2016, we started as a small team of passionate developers\n                  who believed that desktop technology could transform businesses and\n                  improve people&apos;s productivity.\n                </p>\n                <p>\n                  Today, we&apos;ve grown into a full-service desktop development agency,\n                  helping startups and enterprises build world-class desktop applications\n                  that users love and businesses depend on.\n                </p>\n                <p>\n                  Our commitment to quality, innovation, and client success has made us\n                  a trusted partner for companies looking to make their mark in the desktop world.\n                </p>\n              </div>\n            </div>\n            <div className=\"relative\">\n              <div className=\"aspect-square rounded-2xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/10 p-8 flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <div className=\"text-6xl mb-4\">💻</div>\n                  <div className=\"text-2xl font-bold text-white mb-2\">Innovation First</div>\n                  <div className=\"text-gray-400\">Pushing boundaries in desktop development</div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Team */}\n          <motion.div variants={containerVariants}>\n            <motion.div variants={itemVariants} className=\"text-center mb-12\">\n              <h3 className=\"text-3xl md:text-4xl font-bold text-white mb-4\">\n                Meet Our Team\n              </h3>\n              <p className=\"text-gray-300 max-w-2xl mx-auto\">\n                The talented individuals behind our success, each bringing unique expertise\n                and passion to every project.\n              </p>\n            </motion.div>\n\n            <div className=\"grid md:grid-cols-3 gap-8\">\n              {team.map((member, index) => (\n                <motion.div\n                  key={index}\n                  variants={itemVariants}\n                  className=\"group text-center\"\n                >\n                  <div className=\"relative mb-6\">\n                    <div className=\"w-32 h-32 mx-auto rounded-full overflow-hidden border-4 border-white/10 group-hover:border-blue-400/50 transition-colors duration-300\">\n                      <img\n                        src={member.image}\n                        alt={member.name}\n                        className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300\"\n                      />\n                    </div>\n                  </div>\n                  <h4 className=\"text-xl font-bold text-white mb-2\">{member.name}</h4>\n                  <p className=\"text-blue-400 font-medium mb-3\">{member.role}</p>\n                  <p className=\"text-gray-400 text-sm mb-4 leading-relaxed\">{member.bio}</p>\n                  <div className=\"flex justify-center space-x-3\">\n                    <a href={member.social.github} className=\"text-gray-400 hover:text-white transition-colors\" aria-label={`${member.name}'s GitHub profile`}>\n                      <Github className=\"w-5 h-5\" />\n                    </a>\n                    <a href={member.social.linkedin} className=\"text-gray-400 hover:text-white transition-colors\" aria-label={`${member.name}'s LinkedIn profile`}>\n                      <Linkedin className=\"w-5 h-5\" />\n                    </a>\n                    <a href={member.social.twitter} className=\"text-gray-400 hover:text-white transition-colors\" aria-label={`${member.name}'s Twitter profile`}>\n                      <Twitter className=\"w-5 h-5\" />\n                    </a>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\n\n\n// Contact Section\nconst ContactSection = () => {\n  return (\n    <section className=\"py-32 px-8 relative z-10\">\n      <div className=\"max-w-6xl mx-auto\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-6xl font-bold text-white mb-6\">\n            Ready to Transform Your <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600\">Business?</span>\n          </h2>\n          <p className=\"text-xl text-neutral-300 max-w-2xl mx-auto\">\n            Let&apos;s discuss your desktop application needs and create something extraordinary together.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n          {/* Contact Info */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <GlowCard glowColor=\"blue\" customSize={true} className=\"h-full w-full\">\n              <div className=\"p-8 text-white space-y-8\">\n                <h3 className=\"text-2xl font-semibold mb-6\">Get In Touch</h3>\n\n                <div className=\"space-y-6\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center\">\n                      <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-neutral-400\">Email Us</p>\n                      <a href=\"mailto:<EMAIL>\" className=\"text-blue-400 hover:text-blue-300 transition-colors\">\n                        <EMAIL>\n                      </a>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center\">\n                      <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                      </svg>\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-neutral-400\">LinkedIn</p>\n                      <a href=\"https://www.linkedin.com/company/thesemanticsco/\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-400 hover:text-blue-300 transition-colors\">\n                        The Semantics Company\n                      </a>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"w-12 h-12 bg-green-600/20 rounded-lg flex items-center justify-center\">\n                      <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M18.561 13.158c-1.102 0-2.135-.467-3.074-1.227l.228-1.076.008-.042c.207-1.143.849-3.06 2.839-3.06 1.705 0 2.707 1.614 2.707 3.17 0 1.104-.365 2.235-1.708 2.235z\"/>\n                        <path d=\"M15.84 5.77c-.164-.093-.356-.153-.566-.153-.496 0-.934.33-1.073.805l-.017.058c-.01.026-.016.055-.016.085 0 .084.027.162.074.226.047.064.113.113.191.138l.021.007c.049.016.102.024.156.024.496 0 .934-.33 1.073-.805l.017-.058c.01-.026.016-.055.016-.085 0-.084-.027-.162-.074-.226-.047-.064-.113-.113-.191-.138l-.021-.007c-.049-.016-.102-.024-.156-.024-.2 0-.39.06-.566.153z\"/>\n                        <path d=\"M24 12c0 6.627-5.373 12-12 12S0 18.627 0 12 5.373 0 12 0s12 5.373 12 12zM9.5 7.5c0-1.381 1.119-2.5 2.5-2.5s2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5-2.5-1.119-2.5-2.5zm7 9c0 1.933-1.567 3.5-3.5 3.5s-3.5-1.567-3.5-3.5 1.567-3.5 3.5-3.5 3.5 1.567 3.5 3.5z\"/>\n                      </svg>\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-neutral-400\">Upwork Profile</p>\n                      <a href=\"https://www.upwork.com/freelancers/~01e36322be7abf3c0a\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-green-400 hover:text-green-300 transition-colors\">\n                        View Our Upwork Profile\n                      </a>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center\">\n                      <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M23.004 15.588a.995.995 0 0 0-.804-.98l-2.626-.526a.995.995 0 0 0-1.078.54l-1.17 2.05a15.963 15.963 0 0 1-6.345-6.345l2.05-1.17a.995.995 0 0 0 .54-1.078l-.526-2.626a.995.995 0 0 0-.98-.804A10.741 10.741 0 0 0 8.5 4C3.806 4 0 7.806 0 12.5S3.806 21 8.5 21c4.632 0 8.424-3.73 8.504-8.412z\"/>\n                        <path d=\"M12.5 0C5.596 0 0 5.596 0 12.5S5.596 25 12.5 25 25 19.404 25 12.5 19.404 0 12.5 0zm6.25 18.75h-12.5v-12.5h12.5v12.5z\"/>\n                      </svg>\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-neutral-400\">Fiverr Profile</p>\n                      <a href=\"https://www.fiverr.com/tahirccreative\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-purple-400 hover:text-purple-300 transition-colors\">\n                        View Our Fiverr Profile\n                      </a>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </GlowCard>\n          </motion.div>\n\n          {/* CTA */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"flex flex-col justify-center\"\n          >\n            <div className=\"space-y-8\">\n              <div>\n                <h3 className=\"text-3xl font-bold text-white mb-4\">Start Your Project Today</h3>\n                <p className=\"text-neutral-300 text-lg leading-relaxed\">\n                  Ready to bring your vision to life? Contact us for a free consultation and let&apos;s discuss how we can help transform your business with cutting-edge technology solutions.\n                </p>\n              </div>\n\n              <div className=\"space-y-4\">\n                <Button asChild className=\"w-full text-lg px-12 py-4 rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                  <a href=\"mailto:<EMAIL>\">\n                    Get Free Consultation <ArrowRight className=\"ml-2 w-5 h-5\" />\n                  </a>\n                </Button>\n                <Button variant=\"outline\" className=\"w-full text-lg px-12 py-4 rounded-xl bg-transparent text-white border-2 border-white/30 hover:bg-white/10 transition-all duration-300\">\n                  View Our Portfolio\n                </Button>\n              </div>\n\n              <div className=\"pt-6 border-t border-white/10\">\n                <p className=\"text-sm text-neutral-400 text-center\">\n                  Trusted by 500+ clients worldwide • 15+ years of experience • 99% client satisfaction\n                </p>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// Main Component\nconst DemoOne = () => {\n  return (\n    <div className=\"min-h-screen w-screen bg-gradient-to-br from-[#000] to-[#1A2428] relative overflow-x-hidden\">\n      {/* 3D Background - Lazy loaded for better performance */}\n      <div className='fixed inset-0 z-0'>\n        <OptimizedScene />\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10\">\n        <HeroSection />\n        <FeaturesSection />\n        <ServicesSection />\n        <ReviewsSection />\n        <AboutSection />\n        <ContactSection />\n      </div>\n    </div>\n  );\n};\n\nexport { DemoOne };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AA8BA,eAAe;AACf,MAAM,cAAc;IAClB,qBACE,6LAAC;QAAQ,WAAU;QAAmE,cAAW;kBAC/F,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;kCAAkH;;;;;;kCAIvJ,6LAAC;wBAAG,WAAU;;4BAA6E;0CAC1D,6LAAC;gCAAK,WAAU;0CAA6E;;;;;;;;;;;;kCAG9H,6LAAC;wBAAE,WAAU;kCAAyE;;;;;;kCAItF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,WAAU;;oCAAuK;kDACpK,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAE3C,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;0CAAiI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjL;KAlCM;AAoCN,mBAAmB;AACnB,MAAM,kBAAkB;IACtB,MAAM,WAAW;QACf;YACE,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,mMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;QAA2B,cAAW;kBACvD,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,6LAAC;gCAAG,WAAU;;oCAAiD;kDAClD,6LAAC;wCAAK,WAAU;kDAA6E;;;;;;;;;;;;0CAE1G,6LAAC;gCAAE,WAAU;0CAA6C;;;;;;;;;;;;;;;;;8BAM9D,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,gJAAA,CAAA,WAAQ;gCACP,WAAW,QAAQ,KAAK;gCACxB,YAAY;gCACZ,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,QAAQ,IAAI;gDAAC,WAAU;;;;;;0DACxB,6LAAC;gDAAG,WAAU;0DAAyB,QAAQ,KAAK;;;;;;0DACpD,6LAAC;gDAAE,WAAU;0DAA4C,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;2BAf7E;;;;;;;;;;;;;;;;;;;;;AAyBnB;MAvFM;AAyFN,mBAAmB;AACnB,MAAM,kBAAkB;IACtB,MAAM,WAAW;QACf;YACE,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAsB;gBAAgB;gBAAoB;aAAmB;QAC1F;QACA;YACE,MAAM,mMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAkB;gBAAmB;gBAA4B;aAAoB;QAClG;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAsB;gBAAqB;gBAAqB;aAAe;QAC5F;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;;gCAAiD;8CACzD,6LAAC;oCAAK,WAAU;8CAA6E;;;;;;;;;;;;sCAEnG,6LAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAK5D,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,gJAAA,CAAA,WAAQ;gCACP,WAAW,QAAQ,MAAM,IAAI,SAAS;gCACtC,YAAY;gCACZ,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,QAAQ,IAAI;gDAAC,WAAU;;;;;;0DACxB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA+B,QAAQ,KAAK;;;;;;kEAC1D,6LAAC;wDAAE,WAAU;kEAAiD,QAAQ,WAAW;;;;;;;;;;;;0DAEnF,6LAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC9B,6LAAC;wDAAa,WAAU;;0EACtB,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DACtB;;uDAFM;;;;;;;;;;;;;;;;;;;;;;;;;;2BApBd;;;;;;;;;;;;;;;;;;;;;AAmCnB;MA9EM;AAgFN,kBAAkB;AAClB,MAAM,iBAAiB;IACrB,oDAAoD;IACpD,MAAM,wBAAwB,CAAC,MAAc;QAC3C,MAAM,SAAS;YAAC;YAAW;YAAW;YAAW;SAAU;QAC3D,MAAM,QAAQ,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;QAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,WAAW;QACpE,OAAO,AAAC,6BAeL,OAfiC,KAAK,AAAC,gIAII,OADd,OAAM,4FAEU,OADF,OAAM,2EAIA,OAHJ,OAAM,+HAKyE,OAF3E,OAAM,iPAE8E,OAAT,UAAS;IAM1I;IAEA,MAAM,UAAU;QACd;YACE,MAAM;YACN,UAAU;YACV,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,QAAQ,sBAAsB,kBAAkB;QAClD;QACA;YACE,MAAM;YACN,UAAU;YACV,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,QAAQ,sBAAsB,WAAW;QAC3C;QACA;YACE,MAAM;YACN,UAAU;YACV,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,QAAQ,sBAAsB,gBAAgB;QAChD;QACA;YACE,MAAM;YACN,UAAU;YACV,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,QAAQ,sBAAsB,mBAAmB;QACnD;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;;gCAAiD;8CACtD,6LAAC;oCAAK,WAAU;8CAA6E;;;;;;;;;;;;sCAEtG,6LAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAK5D,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,gJAAA,CAAA,WAAQ;gCACP,WAAW,QAAQ,MAAM,IAAI,SAAS,QAAQ,MAAM,IAAI,WAAW;gCACnE,YAAY;gCACZ,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAE,WAAU;;wDAAkD;wDAAQ,OAAO,MAAM;wDAAC;;;;;;;8DACrF,6LAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM,OAAO,MAAM;qDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,qMAAA,CAAA,OAAI;4DAAS,WAAU;2DAAb;;;;;;;;;;;;;;;;sDAIjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,KAAK,OAAO,MAAM;oDAClB,KAAK,OAAO,IAAI;oDAChB,WAAU;;;;;;8DAEZ,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA4B,OAAO,IAAI;;;;;;sEACrD,6LAAC;4DAAE,WAAU;sEAA4B,OAAO,QAAQ;;;;;;sEACxD,6LAAC;4DAAE,WAAU;sEAA4B,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA9B1D;;;;;;;;;;;;;;;;;;;;;AAyCnB;MAzHM;AA2HN,gBAAgB;AAChB,MAAM,eAAe;IACnB,MAAM,QAAQ;QACZ;YAAE,MAAM,uMAAA,CAAA,QAAK;YAAE,QAAQ;YAAQ,OAAO;QAAgB;QACtD;YAAE,MAAM,uMAAA,CAAA,QAAK;YAAE,QAAQ;YAAS,OAAO;QAAgB;QACvD;YAAE,MAAM,uMAAA,CAAA,QAAK;YAAE,QAAQ;YAAM,OAAO;QAAmB;QACvD;YAAE,MAAM,yMAAA,CAAA,SAAM;YAAE,QAAQ;YAAS,OAAO;QAAqB;KAC9D;IAED,MAAM,OAAO;QACX;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,KAAK;YACL,QAAQ;gBAAE,QAAQ;gBAAmC,UAAU;gBAAoD,SAAS;YAAmC;QACjK;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,KAAK;YACL,QAAQ;gBAAE,QAAQ;gBAAqC,UAAU;gBAA+C,SAAS;YAAqC;QAChK;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,KAAK;YACL,QAAQ;gBAAE,QAAQ;gBAAK,UAAU;gBAAK,SAAS;YAAI;QACrD;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,UAAU;YACZ;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;gBAAK;;kCAGvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,6LAAC;gCAAG,WAAU;0CAAuI;;;;;;0CAGrJ,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCAAI,WAAU;kDACZ,KAAK,MAAM;;;;;;kDAEd,6LAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;;+BAXR;;;;;;;;;;kCAkBX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;;0CAEV,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAG/D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAE;;;;;;0DAKH,6LAAC;0DAAE;;;;;;0DAKH,6LAAC;0DAAE;;;;;;;;;;;;;;;;;;0CAMP,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;0DACpD,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;;0CACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;gCAAc,WAAU;;kDAC5C,6LAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAG/D,6LAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;0CAMjD,6LAAC;gCAAI,WAAU;0CACZ,KAAK,GAAG,CAAC,CAAC,QAAQ,sBACjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,UAAU;wCACV,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,KAAK,OAAO,KAAK;wDACjB,KAAK,OAAO,IAAI;wDAChB,WAAU;;;;;;;;;;;;;;;;0DAIhB,6LAAC;gDAAG,WAAU;0DAAqC,OAAO,IAAI;;;;;;0DAC9D,6LAAC;gDAAE,WAAU;0DAAkC,OAAO,IAAI;;;;;;0DAC1D,6LAAC;gDAAE,WAAU;0DAA8C,OAAO,GAAG;;;;;;0DACrE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,MAAM,OAAO,MAAM,CAAC,MAAM;wDAAE,WAAU;wDAAmD,cAAY,AAAC,GAAc,OAAZ,OAAO,IAAI,EAAC;kEACrH,cAAA,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,6LAAC;wDAAE,MAAM,OAAO,MAAM,CAAC,QAAQ;wDAAE,WAAU;wDAAmD,cAAY,AAAC,GAAc,OAAZ,OAAO,IAAI,EAAC;kEACvH,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,6LAAC;wDAAE,MAAM,OAAO,MAAM,CAAC,OAAO;wDAAE,WAAU;wDAAmD,cAAY,AAAC,GAAc,OAAZ,OAAO,IAAI,EAAC;kEACtH,cAAA,6LAAC,2MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;;;;;;;;uCAxBlB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCvB;MA1LM;AA8LN,kBAAkB;AAClB,MAAM,iBAAiB;IACrB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;;gCAAiD;8CACrC,6LAAC;oCAAK,WAAU;8CAA6E;;;;;;;;;;;;sCAEvH,6LAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAK5D,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,gJAAA,CAAA,WAAQ;gCAAC,WAAU;gCAAO,YAAY;gCAAM,WAAU;0CACrD,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAE5C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;gEAAU,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACjE,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAA2B;;;;;;8EACxC,6LAAC;oEAAE,MAAK;oEAA8B,WAAU;8EAAsD;;;;;;;;;;;;;;;;;;8DAM1G,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;gEAAU,MAAK;gEAAe,SAAQ;0EACnD,cAAA,6LAAC;oEAAK,GAAE;;;;;;;;;;;;;;;;sEAGZ,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAA2B;;;;;;8EACxC,6LAAC;oEAAE,MAAK;oEAAmD,QAAO;oEAAS,KAAI;oEAAsB,WAAU;8EAAsD;;;;;;;;;;;;;;;;;;8DAMzK,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;gEAAU,MAAK;gEAAe,SAAQ;;kFACnD,6LAAC;wEAAK,GAAE;;;;;;kFACR,6LAAC;wEAAK,GAAE;;;;;;kFACR,6LAAC;wEAAK,GAAE;;;;;;;;;;;;;;;;;sEAGZ,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAA2B;;;;;;8EACxC,6LAAC;oEAAE,MAAK;oEAAyD,QAAO;oEAAS,KAAI;oEAAsB,WAAU;8EAAwD;;;;;;;;;;;;;;;;;;8DAMjL,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;gEAAU,MAAK;gEAAe,SAAQ;;kFACnD,6LAAC;wEAAK,GAAE;;;;;;kFACR,6LAAC;wEAAK,GAAE;;;;;;;;;;;;;;;;;sEAGZ,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAA2B;;;;;;8EACxC,6LAAC;oEAAE,MAAK;oEAAwC,QAAO;oEAAS,KAAI;oEAAsB,WAAU;8EAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAW1K,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;kDAK1D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,WAAU;0DACxB,cAAA,6LAAC;oDAAE,MAAK;;wDAA8B;sEACd,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAGhD,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;0DAAwI;;;;;;;;;;;;kDAK9K,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpE;MArIM;AAuIN,iBAAiB;AACjB,MAAM,UAAU;IACd,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,uIAAA,CAAA,iBAAc;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;;;;kCACD,6LAAC;;;;;kCACD,6LAAC;;;;;kCACD,6LAAC;;;;;kCACD,6LAAC;;;;;kCACD,6LAAC;;;;;;;;;;;;;;;;;AAIT;MAnBM", "debugId": null}}]}